const { defaults } = require('jest-config');
const { resolve } = require('path');

module.exports = {
  rootDir: '.',
  roots: ['<rootDir>', '<rootDir>/src'],

  modulePaths: ['<rootDir>', '<rootDir>/src', 'test'],
  moduleFileExtensions: [...defaults.moduleFileExtensions, 'ts', 'tsx'],
  transform: {
    '^.+\\.vue$': 'babel-jest',
    '.+\\.(css|styl|less|sass|scss|png|jpg|ttf|woff|woff2|svg|gif)$': 'jest-transform-stub',
    '^.+\\.(ts|tsx)?$': 'babel-jest',
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
    // '^@core/(.*)$': resolve(__dirname, './src/modules/core/$1'),
    '^@src/(.*)$': resolve(__dirname, './src/$1'),
    '^src/(.*)$': resolve(__dirname, './src/$1'),
    // '^@components/(.*)$': resolve(__dirname, './src/components/$1'),
    // '^@constants/(.*)$': resolve(__dirname, './src/constants/$1'),
    // '^@pages/(.*)$': resolve(__dirname, './src/pages/$1'),
    // '^@services/(.*)$': resolve(__dirname, './src/services/$1'),
    // '^@styles/(.*)$': resolve(__dirname, './src/styles/$1'),
    // '^@utils/(.*)$': resolve(__dirname, './src/utils/$1'),
  },

  transformIgnorePatterns: ['navbar/node_modules/'],
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  testMatch: ['**/*.test.ts', '**/*.test.tsx'],
  collectCoverageFrom: [
    '**/*.{ts,tsx}',
    '!**/node_modules/**',
    '!**/vendor/**',
    '!*.test.*',
    '!**/types/**',
    '!**/__tests__/**',
    '!**/__test__/**',
  ],
};
