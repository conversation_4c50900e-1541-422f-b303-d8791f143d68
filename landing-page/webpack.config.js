/* eslint-disable no-undef */
const { merge } = require("webpack-merge");
const singleSpaDefaults = require("./webpack/webpack-react.config");
const path = require('path');
const webpack = require('webpack');
const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');

const { ENV = 'dev' } = process.env
require('dotenv').config({ path: path.resolve(__dirname, '..', 'paris2-configuration.env') });

const ENV_MAP = Object.entries(process.env).reduce(
    (map, [key, value]) => ({
      ...map,
      [key]: JSON.stringify(value || ''),
    }),
    {},
  );

module.exports = (webpackConfigEnv) => {
    const defaultConfig = singleSpaDefaults({
        orgName: "paris2",
        projectName: "landing-page",
        webpackConfigEnv,
    });

    const i18nextExternals = {
        externals: [
            /^react-i18next\/?.*$/
        ]
    };

    const styledComponentExternals = {
        externals: [/^styled-components\/?.*$/]
    };

    return merge(defaultConfig, i18nextExternals, {
        resolve: {
          extensions: ['.ts', '.tsx', '.js', '.json'],
          plugins: [new TsconfigPathsPlugin()],
        },
        module: {
            rules: [
                {
                    test: /\.ts|\.tsx$/,
                    use: 'babel-loader',
                    include: __dirname
                },
                {
                    test: /\.s[ac]ss$/i,
                    use: [
                        // Creates `style` nodes from JS strings
                        {
                          loader: 'style-loader',
                          options: {
                            insert: function insertStyle(element) {
                              var parent = document.querySelector('#paris2-inline-style');
                              if (parent) {
                                element.setAttribute('nonce', parent.getAttribute('nonce'));
                                parent.appendChild(element);
                              } else {
                                var head = document.querySelector('head');
                                head.appendChild(element);
                              }
                            },
                          }
                        },
                        // Translates CSS into CommonJS
                        'css-loader',
                        // Compiles Sass to CSS
                        'sass-loader',
                    ],
                },
                {
                    test: /\.svg$/,
                    use: [
                        {
                            loader: 'svg-url-loader',
                            options: {
                                limit: 10000,
                            },
                        },
                    ],
                },
                {
                    test: /\.(ttf|eot|woff|woff2)$/,
                    use: {
                        loader: "file-loader",
                        options: {
                            name: "[name].[ext]",
                            outputPath: 'fonts',
                            publicPath: '/landing-page/fonts'
                        },
                    },
                },
                {
                    test: /\.(png|jpeg)$/,
                    use: {
                        loader: "file-loader",
                        options: {
                            name: "[name].[ext]",
                            outputPath: 'images',
                            publicPath: '/landing-page/images'
                        },
                    },
                }
            ]
        }
    },
    {
        plugins: [
          new webpack.DefinePlugin({
            'process.env': ENV_MAP,
          }),
        ],
      },
      {
        devServer: {
          port: 9070,
        },
      },
    );
};