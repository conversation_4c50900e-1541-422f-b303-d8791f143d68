import { TimezoneDropdownType } from '../types/widgets';

const { TABLEAU_PROXY_SERVER_HOST } = process.env;

export const DEFAULT_TIMEZONES: TimezoneDropdownType[] = [
  { city: 'Beijing', timezone: "Asia/Hong_Kong" },
  { city: 'Cyprus', timezone: "Asia/Nicosia" },
  { city: 'Hong Kong SAR', timezone: "Asia/Hong_Kong" },
  { city: 'Houston', timezone: "America/Chicago" },
  { city: 'Mumbai', timezone: "Asia/Kolkata" },
  { city: 'London', timezone: "Europe/London" },
  { city: 'Oslo', timezone: "Europe/Oslo" },
  { city: 'Tokyo', timezone: "Asia/Tokyo" },
];

// Cities to show in widget but not present in moment-timezone
export const OTHER_TIMEZONES = [
  { city: 'Mumbai', timezone: "Asia/Kolkata" },
  { city: 'Houston', timezone: "America/Chicago" },
  { city: 'Cyprus', timezone: "Asia/Nicosia" },
];

interface WidgetProps {
  [key: string]: WidgetObject
}

interface WidgetObject {
  tableauLink: string;
  query: string;
}

const TableauLandingWidgets: WidgetProps = {
  owner_finance: {
    tableauLink: `${TABLEAU_PROXY_SERVER_HOST}/views/OwnerFinanceAnalytics/Widget`,
    query: '?tabs:=no&:toolbar=no&:showAppBanner=false&:display_count=n&:showVizHome=n&:origin=viz_share_link&:linktarget=_blank&:jsdebug=n',
  },
  cii_rating: {
    tableauLink: `${TABLEAU_PROXY_SERVER_HOST}/views/CIIRatingDashboard/CIIWidget_to_PARIS2`,
    query: '?tabs:=no&:toolbar=no&:showAppBanner=false&:display_count=n&:showVizHome=n&:origin=viz_share_link&:linktarget=_blank&:jsdebug=n',
  },
};

const DefaultDashboardFilters = {
  Size: 'auto',
  showShareOptions: false,
  tabs: 'no',
  toolbar: 'no',
};

export const DEFAULT_WEATHER_COUNTRY = 'Hong Kong'
export { TableauLandingWidgets, DefaultDashboardFilters };
