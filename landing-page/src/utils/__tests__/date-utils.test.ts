import { dateOrDash } from '../date-utils';
import moment from 'moment';

describe('dateOrDash', () => {
  const DefaultEmptyValue = '- - -';

  test('should return formatted date when input is a valid date string', () => {
    const date = '2024-01-30';
    expect(dateOrDash(date)).toBe(moment(date).format('DD MMM YYYY'));
  });

  test('should return formatted date when input is a valid Date object', () => {
    const date = new Date(2024, 0, 30); // January 30, 2024
    expect(dateOrDash(date)).toBe(moment(date).format('DD MMM YYYY'));
  });

  test('should return formatted date with custom format', () => {
    const date = '2024-01-30';
    expect(dateOrDash(date, 'YYYY/MM/DD')).toBe(moment(date).format('YYYY/MM/DD'));
  });

  test('should return default empty value for invalid date string', () => {
    expect(dateOrDash('invalid-date')).toBe(DefaultEmptyValue);
  });

  test('should return default empty value for null input', () => {
    expect(dateOrDash((null as unknown) as string)).toBe(DefaultEmptyValue);
  });

  test('should return default empty value for undefined input', () => {
    expect(dateOrDash((undefined as unknown) as string)).toBe(moment().format('DD MMM YYYY'));
  });

  test('should return default empty value for an empty string', () => {
    expect(dateOrDash('')).toBe(DefaultEmptyValue);
  });

  test('should return default empty value for non-date input (e.g., number)', () => {
    expect(dateOrDash((12345 as unknown) as string)).toBe('01 Jan 1970');
  });
});
