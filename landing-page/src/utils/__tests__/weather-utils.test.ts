import axios from 'axios';
import { getCurrentWeather } from '../weather-utils';

jest.mock('axios');

describe('getCurrentWeather function', () => {
  it('should return weather data for a valid query', async () => {
    const mockData = { location: { name: 'New York' }, current: { temp_c: 25 } };
    (axios as jest.MockedFunction<typeof axios>).mockResolvedValue({ data: mockData });

    const result = await getCurrentWeather('New York');
    expect(result).toEqual(mockData);
    expect(axios).toHaveBeenCalledWith(
      expect.stringContaining('New York'),
      expect.objectContaining({ timeout: 5000 }),
    );
  });

  it('should throw an error if API request fails', async () => {
    (axios as jest.MockedFunction<typeof axios>).mockRejectedValue(new Error('Network Error'));

    await expect(getCurrentWeather('Invalid')).rejects.toThrow('Network Error');
  });
});
