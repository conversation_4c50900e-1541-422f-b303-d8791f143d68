import { getLocalGreetingsString, getLocalDateStr, getUtcDateStr } from '../datetime-utils';
import moment from 'moment';

describe('getLocalGreetingsString', () => {
  it('should return "Good Morning" when current time is in the morning', () => {
    jest.spyOn(moment, 'now').mockReturnValue(moment('2025-01-30 08:00:00').valueOf());
    expect(getLocalGreetingsString()).toBe('Good Morning');
  });

  it('should return "Good Afternoon" when current time is in the afternoon', () => {
    jest.spyOn(moment, 'now').mockReturnValue(moment('2025-01-30 15:00:00').valueOf());
    expect(getLocalGreetingsString()).toBe('Good Afternoon');
  });

  it('should return "Good Evening" when current time is in the evening', () => {
    jest.spyOn(moment, 'now').mockReturnValue(moment('2025-01-30 19:00:00').valueOf());
    expect(getLocalGreetingsString()).toBe('Good Evening');
  });

  it('should return "Good Morning" when current time is exactly midnight', () => {
    jest.spyOn(moment, 'now').mockReturnValue(moment('2025-01-30 00:00:00').valueOf());
    expect(getLocalGreetingsString()).toBe('Good Morning');
  });

  it('should return "Good Evening" when current time is exactly 6:00 PM', () => {
    jest.spyOn(moment, 'now').mockReturnValue(moment('2025-01-30 18:00:00').valueOf());
    expect(getLocalGreetingsString()).toBe('Good Evening');
  });
});

describe('getLocalDateStr', () => {
  it('should return the correctly formatted local date string with a custom format', () => {
    const date = new Date('2025-01-30T12:00:00Z');
    const customFormat = 'MM/DD/YYYY';
    const formattedDate = '01/30/2025';
    expect(getLocalDateStr(date, customFormat)).toBe(formattedDate);
  });
});

describe('getUtcDateStr', () => {
  it('should return the correctly formatted UTC date string', () => {
    const date = new Date('2025-01-30T12:00:00Z');
    const formattedDate = '2025-01-30 12:00:00';
    expect(getUtcDateStr(date)).toBe(formattedDate);
  });

  it('should return the correctly formatted UTC date string with a custom format', () => {
    const date = new Date('2025-01-30T12:00:00Z');
    const customFormat = 'MM/DD/YYYY';
    const formattedDate = '01/30/2025';
    expect(getUtcDateStr(date, customFormat)).toBe(formattedDate);
  });
});
