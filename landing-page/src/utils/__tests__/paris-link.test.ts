import _ from 'lodash';
import {
  goToParis1ShipDetails,
  goToParis2ShipDetails,
  goToParis1Qualship,
  goToParisShipDetails,
} from '../paris-link';

global.open = jest.fn();

process.env.PARIS_ONE_HOST = 'https://paris.fleetship.com';
process.env.PARIS_TWO_HOST = 'https://paris2.fleetship.com';

describe('Paris Ship Details Functions', () => {
  const PARIS_ONE_HOST = 'https://paris.fleetship.com';
  const PARIS_TWO_HOST = 'https://paris2.fleetship.com';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should open the correct URL for goToParis1ShipDetails', () => {
    const paris1RefID = 123;
    const shipPartyType = 'vessel';

    goToParis1ShipDetails(paris1RefID, shipPartyType);

    expect(window.open).toHaveBeenCalledWith(
      `${PARIS_ONE_HOST}/fml/FMLLoginKeycloak?targeturl=/fml/PARIS?display=ship&shipid=${paris1RefID}`,
      '_blank',
    );
  });

  it('should open the correct URL for goToParis2ShipDetails without category', () => {
    const paris2ID = 456;

    goToParis2ShipDetails(paris2ID);

    expect(window.open).toHaveBeenCalledWith(
      `${PARIS_TWO_HOST}/vessel/ownership/details/${paris2ID}`,
      '_blank',
    );
  });

  it('should open the correct URL for goToParis2ShipDetails with category "Itinerary"', () => {
    const paris2ID = 456;
    const category = 'Itinerary';

    goToParis2ShipDetails(paris2ID, category);

    expect(window.open).toHaveBeenCalledWith(
      `${PARIS_TWO_HOST}/vessel/ownership/details/${paris2ID}/itinerary`,
      '_blank',
    );
  });

  it('should open the correct URL for goToParis1Qualship when shipPartyID is provided', () => {
    const shipPartyID = 789;
    const shipPartyType = 'vessel';

    goToParis1Qualship(shipPartyID, shipPartyType);

    expect(window.open).toHaveBeenCalledWith(
      `${PARIS_ONE_HOST}/fml/FMLLoginKeycloak?targeturl=/fml/PARIS?display=dashboard`,
      '_blank',
    );
  });

  it('should open the correct URL for goToParis1Qualship when shipPartyID is not provided', () => {
    const shipPartyID = null;
    const shipPartyType = 'vessel';

    goToParis1Qualship(shipPartyID, shipPartyType);

    expect(window.open).toHaveBeenCalledWith(
      `${PARIS_ONE_HOST}/fml/PARIS?display=dashboard`,
      '_blank',
    );
  });

  it('should open the correct URL for goToParisShipDetails with ga4EventTrigger and no shipPartyID', () => {
    const vessel = { ownership_id: 111, vessel_name: 'Vessel A' };
    const shipPartyID = null;
    const category = 'Vessel';
    const ga4EventTrigger = jest.fn();

    goToParisShipDetails(vessel, shipPartyID, ga4EventTrigger, category);

    expect(window.open).toHaveBeenCalledWith(
      `${PARIS_TWO_HOST}/vessel/ownership/details/${vessel.ownership_id}`,
      '_blank',
    );
    expect(ga4EventTrigger).toHaveBeenCalledWith(
      'Landing Vessel – Menu',
      'Vessel A',
      'Link to Vessel Details',
    );
  });
});
