import { getActiveOwnershipVessels } from '../vessel';
import { VesselOwnedCurrent } from 'src/services/vessel-service';

describe('getActiveOwnershipVessels function', () => {
  it('should return only vessels with a non-null owner_start_date', () => {
    const vessels: VesselOwnedCurrent[] = [
      { name: 'Vessel A', owner_start_date: '2023-01-01' },
      { name: 'Vessel B', owner_start_date: null },
      { name: 'Vessel C', owner_start_date: '2022-05-10' },
    ];

    const result = getActiveOwnershipVessels(vessels);
    expect(result).toHaveLength(2);
    expect(result).toEqual([
      { name: 'Vessel A', owner_start_date: '2023-01-01' },
      { name: 'Vessel C', owner_start_date: '2022-05-10' },
    ]);
  });

  it('should return an empty array if no vessels have an owner_start_date', () => {
    const vessels: VesselOwnedCurrent[] = [
      { name: 'Vessel A', owner_start_date: null },
      { name: 'Vessel B', owner_start_date: null },
    ];

    const result = getActiveOwnershipVessels(vessels);
    expect(result).toHaveLength(0);
  });
});
