import { ColoredTileTheme } from 'src/components/common/WidgetBadges';
import { VesselOwned } from '../types/vessel';
import { CertificateType } from 'src/types/types';

export function compare(a: VesselOwned, b: VesselOwned) {
  return a.name.localeCompare(b.name);
}

export function getOfrStatusLabelTheme(status: string): [string, ColoredTileTheme] {
  switch (status) {
    case 'ON_GOING':
      return ['Ongoing', 'yellow'];
    case 'UNDER_REVIEW':
      return ['Under Review', 'blue'];
    default:
      return ['-', 'red'];
  }
}

export function getRaStatusLabelTheme(status: string): [string, ColoredTileTheme] {
  switch (status) {
    case 'Critical':
      return ['Critical', 'red'];
    case 'Special':
      return ['Special', 'yellow'];
    case 'Unassigned':
      return ['Unassigned', 'blue-2'];
    default:
      return ['-', 'gray']; // Using a new theme for the default case
  }
}

const certificateTypeLabels: Record<CertificateType, 'Statutory' | 'Important' | 'Ancillary'> = {
  [CertificateType.STATUTORY]: 'Statutory',
  [CertificateType.IMPORTANT]: 'Important',
  [CertificateType.ANCILLARY]: 'Ancillary',
};

export function mapCertificateType(type: CertificateType): 'Statutory' | 'Important' | 'Ancillary' {
  return certificateTypeLabels[type];
}
