/* eslint-disable max-len */
import _ from 'lodash';
import { VesselSubscription } from '../types/ship-party';
import { ShipPartyTypes } from '../constants/ship-party-types';

const { PARIS_ONE_HOST, PARIS_TWO_HOST } = process.env;

interface ParisVesselID {
  id: number;
  vessel_id: number;
  ref_id?: number | null;
}

export const goToParis1ShipDetails = (paris1RefID: number, shipPartyType: string) => {
  const link = '/FMLLoginKeycloak?targeturl=/fml';
  window.open(`${PARIS_ONE_HOST}/fml${link}/PARIS?display=ship&shipid=${paris1RefID}`, '_blank');
};

export const goToParis2ShipDetails = (paris2ID: number, category?: string) => {
  if (category === 'Itinerary') {
    window.open(`${PARIS_TWO_HOST}/vessel/ownership/details/${paris2ID}/itinerary`, '_blank');
  } else {
    window.open(`${PARIS_TWO_HOST}/vessel/ownership/details/${paris2ID}`, '_blank');
  }
};

export const goToParis1Qualship = (shipPartyID: number, shipPartyType: string) => {
  const link = shipPartyID ? '/FMLLoginKeycloak?targeturl=/fml' : '';
  window.open(`${PARIS_ONE_HOST}/fml${link}/PARIS?display=dashboard`, '_blank');
};

export const goToParisShipDetails = (
  vessel: VesselSubscription | ParisVesselID,
  shipPartyID: number | null,
  ga4EventTrigger?: (category: string, label: string, action: string) => void,
  category?: string,
) => {
  if (!shipPartyID) {
    goToParis2ShipDetails(vessel.ownership_id ?? vessel.vessel_id, category);
    if (ga4EventTrigger)
      ga4EventTrigger(
        `Landing ${_.toString(category)} – Menu`,
        _.toString(vessel.vessel_name),
        `Link to ${category === 'Vessel' ? 'Vessel Details' : 'Vessel Itinerary'}`,
      );
    return;
  }
  if (ga4EventTrigger)
    ga4EventTrigger(
      'link to p1',
      'Link',
      `${_.toString(category)} - ${_.toString(vessel.vessel_name)}`,
    );
  goToParis1ShipDetails(vessel.ref_id as number, shipPartyType as string);
};
