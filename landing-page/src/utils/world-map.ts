import { OwnershipDetails, VesselStratumResponse } from "../services/vessel-service";

const getVesselOwnershipMap = (ownerships: OwnershipDetails[]) => Object.fromEntries(ownerships.map((ownership) => [ownership.vessel.id, ownership.id]));

const getOwnershipVesselMap = (ownerships: OwnershipDetails[]) => Object.fromEntries(ownerships.map((ownership) => [ownership.id, ownership.vessel.id]));

const getMissingVesselIds = (vesselList: OwnershipDetails[], positionList: VesselStratumResponse[]) => {
  const vesselIds = vesselList.map((vessel) => vessel.vessel.id);
  const positionVesselIds = Object.keys(positionList).map((vesselId) => parseInt(vesselId, 10));
  return vesselIds.filter((vesselId) => !positionVesselIds.includes(vesselId));
};

const getMissingOwnershipIds = (vesselList: OwnershipDetails[], positionList: VesselStratumResponse[]) => {
  const missingVesselIds = getMissingVesselIds(vesselList, positionList);
  const vesselOwnershiplMap = getVesselOwnershipMap(vesselList);
  return missingVesselIds.map((vesselId) => vesselOwnershiplMap[vesselId]);
};

export { getVesselOwnershipMap, getOwnershipVesselMap, getMissingVesselIds, getMissingOwnershipIds };
