import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import VesselTable from '../../components/dashboard/VesselTable';

jest.mock('../../StyleGuide.tsx');
jest.mock('../../services/seafarer-service');
jest.mock('../../services/user-service');
jest.mock('../../services/vessel-service');

const mockVisitVessel = jest.fn();
const mockVisitCrewList = jest.fn();
const mockVisitEuEts = jest.fn();

const mockComplianceData = [
  {
    vessel_id: 505,
    ocimf_sr: {
      issues: [
        {
          company: { is_compliant: false, rank_group: 'MASTER,CO' },
          vessel_type: { is_compliant: false, rank_group: 'MASTER,CO' },
          rank: { is_compliant: true, rank_group: 'MASTER,CO' },
        },
      ],
      badge: 'red',
    },
    document_issues: { count: 5, badge: 'red' },
    ocimf_jr: {
      issues: [
        {
          company: { is_compliant: false, rank_group: '2O,3O' },
          vessel_type: { is_compliant: false, rank_group: '2O,3O' },
          rank: { is_compliant: false, rank_group: '2O,3O' },
        },
      ],
      badge: 'yellow',
    },
    experience_issues: {
      ranks: [['MASTER', 'CO']],
      badge: 'orange',
    },
  },
];

const compliantVessels = [{ id: '1', name: 'Vessel A', vessel_type: 'tanker' }];
const nonCompliantVessels = [
  { id: '505', name: 'Vessel B', ocimf: mockComplianceData[0], vessel_type: 'tanker' },
];

const renderComponent = (props = {}) => {
  return render(
    <VesselTable
      compliantVessels={compliantVessels}
      nonCompliantVessels={nonCompliantVessels}
      isPreComputeApiCalled={true}
      visitVessel={mockVisitVessel}
      visitCrewList={mockVisitCrewList}
      visitEuEts={mockVisitEuEts}
      {...props}
    />,
  );
};

test('renders VesselTable with compliant and non-compliant vessels', () => {
  renderComponent();
  expect(screen.getByText('Compliant Vessels')).toBeInTheDocument();
  expect(screen.getByText('Non-Compliant OCIMF Tankers')).toBeInTheDocument();
  expect(screen.getByText('Vessel A')).toBeInTheDocument();
  expect(screen.getByText('Vessel B')).toBeInTheDocument();
});

test('renders no results found when no vessels exist', () => {
  renderComponent({ compliantVessels: [], nonCompliantVessels: [] });
  expect(screen.getByText('No Results Found')).toBeInTheDocument();
});

test('calls visitVessel when clicking on Vessel Details', () => {
  renderComponent();
  fireEvent.click(screen.getByTestId('more-options-505'));
  fireEvent.click(screen.getByTestId('vessel-details'));
  expect(mockVisitVessel).toHaveBeenCalledWith(nonCompliantVessels[0]);
});

test('calls visitCrewList when clicking on Crew List', () => {
  renderComponent();
  fireEvent.click(screen.getByTestId('more-options-505'));
  fireEvent.click(screen.getByTestId('crew-list'));
  expect(mockVisitCrewList).toHaveBeenCalledWith(nonCompliantVessels[0], true);
});

test('calls visitEuEts when clicking on EU-ETS Report', () => {
  renderComponent();
  fireEvent.click(screen.getByTestId('more-options-505'));
  fireEvent.click(screen.getByTestId('eu-ets-report'));
  expect(mockVisitEuEts).toHaveBeenCalledWith(nonCompliantVessels[0]);
});
