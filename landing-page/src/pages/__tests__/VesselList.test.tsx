import React from 'react';
import { act, cleanup, fireEvent, render, screen, waitFor } from '@testing-library/react';
import VesselList from '../../components/dashboard/VesselList';
import * as vesselService from '../../services/vessel-service';
import * as seafarerService from '../../services/seafarer-service';
import { GlobalContext } from '../../context/dashboard-context';

jest.mock('../../StyleGuide.tsx');
jest.mock('../../services/seafarer-service');
jest.mock('../../services/user-service');
jest.mock('../../services/vessel-service', () => ({
  getOwnerships: jest.fn(),
}));

const mockVesselList = [
  {
    id: 2201,
    name: 'Chemical Sailor',
    vessel: { id: 1269 },
    vessel_type: { type: 'tanker' },
    temp_ref_id: 5391,
  },
  {
    id: 2200,
    name: '<PERSON><PERSON>yx<PERSON>',
    vessel: { id: 980 },
    vessel_type: { type: 'dry' },
    temp_ref_id: 4830,
  },
  {
    id: 2195,
    name: 'Ocean Sukses (New)',
    vessel: { id: 1203 },
    vessel_type: { type: 'dry' },
    temp_ref_id: 5312,
  },
];

const mockComplianceData = [
  {
    vessel_id: 1269,
    ocimf_sr: {
      issues: [
        {
          company: { is_compliant: false, rank_group: 'MASTER,CO' },
          vessel_type: { is_compliant: false, rank_group: 'MASTER,CO' },
          rank: { is_compliant: true, rank_group: 'MASTER,CO' },
        },
      ],
      badge: 'red',
    },
    document_issues: { count: 5, badge: 'red' },
    ocimf_jr: {
      issues: [
        {
          company: { is_compliant: false, rank_group: '2O,3O' },
          vessel_type: { is_compliant: false, rank_group: '2O,3O' },
          rank: { is_compliant: false, rank_group: '2O,3O' },
        },
      ],
      badge: 'yellow',
    },
    experience_issues: {
      ranks: [['MASTER', 'CO']],
      badge: 'orange',
    },
  },
];

const ga4EventTrigger = (category, label, action) => {
  console.log(category, label, action);
};

beforeAll(() => {
  process.env = Object.assign(process.env, {
    PARIS_ONE_HOST: 'https://parisdev.fleetship.com',
    BASE_URL: 'https://example.com',
  });
});

afterEach(cleanup);

describe('VesselList Component Tests', () => {
  beforeEach(() => {
    jest
      .spyOn(vesselService, 'getOwnerships')
      .mockResolvedValue({ data: { results: mockVesselList } });
    jest
      .spyOn(seafarerService, 'getPrecomputedCompliances')
      .mockResolvedValue({ data: mockComplianceData });
  });

  test('Should render the component and display title', () => {
    render(
      <GlobalContext.Provider
        value={{ vesselCurrentlyOwned: mockVesselList, isVesselLoading: false, ga4EventTrigger }}
      >
        <VesselList shipPartyId={10} />
      </GlobalContext.Provider>,
    );

    const title = screen.getByText('Vessels');
    expect(title).toBeInTheDocument();
  });

  test('Should fetch and display vessel names', async () => {
    render(
      <GlobalContext.Provider
        value={{ vesselCurrentlyOwned: mockVesselList, isVesselLoading: false, ga4EventTrigger }}
      >
        <VesselList shipPartyId={10} />
      </GlobalContext.Provider>,
    );

    await waitFor(() => {
      expect(screen.getByText('Chemical Sailor')).toBeInTheDocument();
      expect(screen.getByText('Spar Pyxis')).toBeInTheDocument();
      expect(screen.getByText('Ocean Sukses (New)')).toBeInTheDocument();
    });
  });

  test('Should filter vessels by name when searching', async () => {
    render(
      <GlobalContext.Provider value={{}}>
        <VesselList shipPartyId={10} />
      </GlobalContext.Provider>,
    );

    act(() => {
      fireEvent.change(screen.getByTestId('search-vessel-dropdown'), { target: { value: 'Spar' } });
    });

    await waitFor(() => {
      expect(screen.getByText('Spar Pyxis')).toBeInTheDocument();
      expect(screen.queryByText('Chemical Sailor')).toBeNull();
    });
  });

  test('Should show no results when search does not match any vessel', async () => {
    render(
      <GlobalContext.Provider
        value={{ vesselCurrentlyOwned: mockVesselList, isVesselLoading: false, ga4EventTrigger }}
      >
        <VesselList shipPartyId={10} />
      </GlobalContext.Provider>,
    );

    act(() => {
      fireEvent.change(screen.getByTestId('search-vessel-dropdown'), {
        target: { value: 'invalid' },
      });
    });

    await waitFor(() => {
      expect(screen.getByText('No Results Found')).toBeInTheDocument();
    });
  });

  test('Should show empty state when no vessels are available', () => {
    render(
      <GlobalContext.Provider
        value={{ vesselCurrentlyOwned: [], isVesselLoading: false, ga4EventTrigger }}
      >
        <VesselList shipPartyId={10} />
      </GlobalContext.Provider>,
    );

    expect(screen.getByTestId('vessel-loading-wrapper')).toBeInTheDocument();
  });

  test('Should clear vessel search when clicking clear button', async () => {
    render(
      <GlobalContext.Provider value={{ ga4EventTrigger }}>
        <VesselList shipPartyId={10} />
      </GlobalContext.Provider>,
    );

    act(() => {
      fireEvent.change(screen.getByTestId('search-vessel-dropdown'), { target: { value: 'Spar' } });
    });

    await waitFor(() => {
      expect(screen.getByTestId('search-vessel-dropdown')).toHaveValue('spar');
    });

    act(() => {
      fireEvent.click(screen.getByTestId('clear-icon-id'));
    });

    await waitFor(() => {
      expect(screen.getByTestId('search-vessel-dropdown')).toHaveValue('');
    });
  });

  test('Should call getPrecomputedCompliances only when vesselList is not empty', async () => {
    const mockGetPrecomputedCompliances = jest.spyOn(seafarerService, 'getPrecomputedCompliances');

    render(
      <GlobalContext.Provider
        value={{ vesselCurrentlyOwned: [], isVesselLoading: false, ga4EventTrigger }}
      >
        <VesselList shipPartyId={10} />
      </GlobalContext.Provider>,
    );

    expect(mockGetPrecomputedCompliances).toHaveBeenCalled();

    cleanup();

    render(
      <GlobalContext.Provider
        value={{ vesselCurrentlyOwned: mockVesselList, isVesselLoading: false, ga4EventTrigger }}
      >
        <VesselList shipPartyId={10} />
      </GlobalContext.Provider>,
    );

    await waitFor(() => expect(mockGetPrecomputedCompliances).toHaveBeenCalled());

    mockGetPrecomputedCompliances.mockRestore();
  });

  test('Should show the last updated timestamp if API call is completed', async () => {
    jest.spyOn(seafarerService, 'getPrecomputedCompliances');
    render(
      <GlobalContext.Provider
        value={{ vesselCurrentlyOwned: mockVesselList, isVesselLoading: false, ga4EventTrigger }}
      >
        <VesselList shipPartyId={10} />
      </GlobalContext.Provider>,
    );

    await waitFor(() => {
      const lastUpdatedText = screen.getByText(/Last Updated on:/);
      expect(lastUpdatedText).toBeInTheDocument();
    });
  });
});
