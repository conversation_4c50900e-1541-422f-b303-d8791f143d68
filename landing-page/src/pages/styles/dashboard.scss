.landing-page-container {
  margin-top: 60px;
  height: 100%;
  background: #f8f9fa 0% 0% no-repeat padding-box !important;
}

.overview-title {
  color: #ffffff;
  letter-spacing: 0px;
  opacity: 1 !important;
  font-size: 22px;
  font-weight: 500;
}

.tableau-widget-container {
  width: 100% !important;
  height: 100% !important;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #cccccc;
  border-radius: 4px;
  opacity: 1;
  position: relative;
}

.report-container {
  width: 100% !important;
  min-height: 414px !important;
  height: 100%;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid rgba(204, 204, 204, 0.5);
  border-radius: 0;
  opacity: 1;
  @media screen and (max-width: 767px) {
    margin-bottom: 2%;
  }
}

.tableau-widget-title {
  letter-spacing: 0px;
  text-align: center;
  font: normal normal bold 14px/17px Inter, sans-serif;
  color: #1f4a70;
  opacity: 1;
  margin-top: 5px;
}

.card-caption {
  font: normal normal 300 14px/17px Inter, sans-serif;
  letter-spacing: 0px;
  color: #6c757d;
  opacity: 1;
}

.card-content {
  display: flex;
  flex-direction: row;
}

.tableau-widget-dashboard {
  height: 250px;
  width: 100%;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 1px solid #cccccc;
  border-radius: 4px;
  opacity: 1;
}

.divider {
  padding-top: 1%;
  padding-bottom: 1%;
  min-width: 100% !important;
}

.tableau-widget-content {
  padding: 20px !important;
  padding-top: 2rem !important;
}

.report-widget-content {
  padding: 20px !important;
  padding-top: 2rem !important;
}

.report-title {
  text-align: center;
  font: normal normal bold 16px/20px Inter, sans-serif;
  letter-spacing: 0px;
  color: #1f4a70;
  opacity: 1;
  font-size: 16px;
  font-weight: 600;
}

.tableau-widget {
  background: #ffffff 0% 0% no-repeat padding-box;
  padding: 1.5%;
}

.live-feed {
  padding: 1.5%;
}

.hr-line-vessel {
  border-top: 1.5px solid #1f4a70;
  margin-left: -1.25rem;
  margin-right: -1.25rem;
  margin-top: 0.5rem;
  margin-bottom: 0;
}

.hr-line-padding {
  border-top: 1.5px solid #1f4a70;
  margin-top: 6.5rem;
}

.hr-line-report {
  border-top: 1.5px solid #1f4a70;
  margin-left: -1.25rem;
  margin-right: -1.25rem;
  margin-top: 4rem;
  @media screen and (max-width: 767px) {
    margin-top: 0;
  }
}

.vessel-title {
  text-align: left;
  font: normal normal bold 14px/34px Inter, sans-serif;
  letter-spacing: 0px;
  color: #1f4a70;
  margin-top: 2px;
  margin-bottom: 2px;
  cursor: pointer;
  width: 100%;
}

.rotate90 {
  transform: rotate(180deg);
}

.item-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
}

.play-btn {
  font-size: 100px;
}
.video-area-header {
  font-size: 23px;
  font-weight: bold;
  text-align: center;
  position: absolute;
  width: 95%;
  top: 20px;
  color: white;
  padding: 5px;
}

.tableau-widget-dashboard img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.tableau-widget-carousel {
  @media screen and (min-width: 768px) {
    display: none;
  }
}

.vessel-table-home {
  width: 100%;
  height: 523px;
  display: flex;
  justify-content: center;
  .page-number-border {
    border: 1px solid #cccccc;
    border-radius: 4px;
    padding: 0px 8px;
    font-size: 14px;
  }

  .page-num-active {
    text-decoration: underline;
  }

  .page-num-inactive {
    text-decoration: initial;
  }

  .page-num-disabled {
    color: #cccccc;
  }

  .page-num-enabled {
    color: #1f4a70;
  }

  .page-num {
    display: inline-block;
    cursor: pointer;
    margin: 8px;
    user-select: none;
  }

  .page-size {
    font-size: 14px;
  }

  .table {
    border-spacing: 0;
    width: 100%;
  }
}

.vessel-loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1 1 0%;
  padding-top: 2rem;
}

.coming-soon-text {
  text-align: center;
  font: normal normal bold 16px/20px Inter, sans-serif;
  letter-spacing: 0px;
  color: #1f4a70;
  opacity: 1;
  display: block;
}

.report-widget-content {
  text-align: center;
  margin-top: 4em;
}

.no-results-found {
  font-size: 26px;
  color: #1f4a70;
  margin-bottom: 20px;
  margin-top: 200px;
}

.tableau-widget-container {
  iframe {
    width: 100%;
    max-height: 12rem;
  }
}

.max-width-wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.video-responsive {
  overflow: hidden;
  padding-bottom: 56.25%;
  position: relative;
  height: 0;
}

.video-responsive iframe {
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  position: absolute;
  display: block;
}

.padding-container {
  width: calc((100% - 1024px) / 2);
  padding-left: 0;
  padding-right: 0;
  @media screen and (max-width: 1023px) {
    display: none;
  }
}

.tableau-loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1 1 0%;
  padding-top: 2rem;
  height: 12rem;
}

.cursor-pointer {
  cursor: pointer;
}

.get-started-button {
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 4px solid #0091b8;
  border-radius: 30px;
  color: #0091b8;
  font: normal normal bold 28px/34px Inter, sans-serif;
  position: absolute;
  left: 11%;
  top: 6%;
  z-index: 1054;
  padding: 13px 62px;
}

.show-page-size {
  margin: auto;
  max-width: 7rem;
}

.stat-number {
  color: #1f4a70;
  font-size: 20px;
  font-weight: bold;
}

.crew-injury-statistics {
  font-size: 14px;
}

.analog-clock-font {
  font-size: 14px;
}

.analog-clock-city {
  color: #1f4a70;
  font-weight: bold;
  font-size: 14px;
}

.statistics-container {
  text-align: center;
  display: flex;
  flex-direction: column;
  padding-top: 1.25rem;
}

.transparent-no-pt {
  padding-top: 0px !important;
  background-color: transparent;
}

.welcome-text-nova {
  font: normal normal bold 16px/20px Inter, sans-serif;
  letter-spacing: 0px;
  color: #1f4a70;

  a {
    font: normal normal bold 16px/20px Inter, sans-serif;
    letter-spacing: 0px;
    color: #0091b8;
    text-decoration: underline !important;
  }
}

.py-3 {
  padding-bottom: 0 !important;
  padding-top: 0 !important;
}

.world-map-container {
  padding-bottom: 1.5rem;
}

.world-map-vessel-list {
  margin-right: 10px;
  .form-control-lg {
    font-size: 15px;
    width: 16rem;
  }
}

.tableau-widget-title {
  margin-top: 1rem;
}

.widget-border {
  border: 1px solid #efefef;
}

.widget-padding-top {
  padding-top: 1rem;
}

@media only screen and (max-width: 728px) {
  .py-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}

.banner {
  padding-top: 1.5rem;
  width: 98.5%;
}

.form-check {
  label {
    color: #0091b8 !important;
    font-weight: bold !important;
  }
}

.check-condition {
  cursor: pointer;
  .form-check-label {
    padding-left: 0.25rem;
  }
  input[type='checkbox'] {
    /* Double-sized Checkboxes */
    -ms-transform: scale(2); /* IE */
    -moz-transform: scale(2); /* FF */
    -webkit-transform: scale(2); /* Safari and Chrome */
    -o-transform: scale(2); /* Opera */
    padding: 10px;
    padding-right: 20px;
  }
}

.terms-condition-modal {
  max-width: 90%;
}

.btn-secondary.disabled,
.btn-secondary:disabled {
  opacity: 0.25 !important;
}

.vessel-list-title-wrapper {
  display: flex;
  justify-content: space-between;
  place-items: center;
}

.report-title-wrapper {
  display: grid;
  justify-content: flex-start;
}

.last-updated-at {
  font-size: 14px;
  font-weight: 400;
  color: #6c757d;
  margin-top: 5px;
}

.align-left {
  text-align: left;
}

.table-vessel-name {
  color: #1f4a70;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
}

.table-vessel-row {
  margin: 8px 15px;
  padding-bottom: 6px;
  border-bottom: 1px #dadada solid;
  display: flex;
  justify-content: space-between;
}

.vessel-table-wrapper {
  max-height: 525px;
  overflow-y: scroll;
  overflow-x: hidden;
  width: 100%;
}

.vessel-table-subheader {
  width: 100%;
  text-align: left;
  font-size: 14px;
  font-weight: 400;
  margin: 10px 15px;
  margin-top: 20px;
}

.ocimf-status-pills-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.ocimf-status-pill {
  border-radius: 50px;
  width: fit-content;
  padding: 2px 8px;
  margin-bottom: 5px;
  margin-right: 3px;
  font-size: 12px;
  word-break: normal;
  font-weight: 500;
  cursor: pointer;
}

.ocimf-tooltip-header {
  font-weight: bold;
}

.ocimf-tooltip-single-content-wrapper {
  display: grid;
  margin-bottom: 10px;
}

.ocimf-tooltip-wrapper {
  display: grid;
  text-align: left !important;
}

.vessel-table-list-popover {
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  margin-top: 5px;
  margin-bottom: 5px;
  width: fit-content;
  display: grid;
  li {
    cursor: pointer;
    padding: 5px 10px 5px 10px;
  }
}

.table-vessel-right-content {
  display: flex;
}

.table-more-options-wrapper {
  margin-left: 10px;
  padding: 0 3px;
  margin-top: -2px;
}

.table-more-options-wrapper:hover {
  background-color: #f3f6f8;
  border-radius: 5px;
  margin-top: -2px;
}

.search-text-with-icon-wrapper {
  display: flex;
  max-width: 300px;
  padding-right: 0;
  margin-right: 10px;
}

.search-icon-wrapper {
  width: 42px;
  height: 38px;
  background: transparent;
  border-right: none;
  margin-right: -5px;
}

.clear-icon-wrapper {
  width: 42px;
  height: 38px;
  background: transparent;
  border-left: none;
  margin-left: -5px;
}

.vessel-search-input {
  width: 270px;
  font-size: 14px;
  border-left: none;
  border-right: none;
  height: 38px !important;
  padding-left: 0;
}

.vessel-search-input:focus {
  box-shadow: none !important;
  border: 1px solid #ced4da;
  border-left: none;
  border-right: none;
}

#vessel-table-list-popover .arrow {
  display: none;
}

#vessel-table-list-popover {
  margin-left: -50px !important;
  display: grid;
}

textarea:focus,
input:focus {
  outline: none;
}

*::-webkit-scrollbar {
  background-color: transparent;
  width: 15px;
}

*::-webkit-scrollbar-track {
  background-color: transparent;
}

*::-webkit-scrollbar-thumb {
  border-radius: 20px;
  border: 4px solid transparent;
  background-color: rgba(0, 0, 0, 0.2);
  background-clip: content-box;
}

.vessel-table-list-popover li:hover {
  background: #e9f1f5;
}

input.vessel-search-input.form-control {
  background: white !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition: background-color 5000s ease-in-out 0s;
}

.stat-card-wrapper {
  display: flex;
  flex-direction: row;
  padding: 25px;
  gap: 20px;
}

.vm-landing-page-container {
  margin-top: 60px;
  height: 100%;
}

.widget-border-vm {
  border: 1px solid #dee2e6 !important;
  border-radius: 5.2px !important;
  box-shadow: 0px 0px 3.47px 0px rgba(0, 0, 0, 0.149);
  background: #ffffff;
}