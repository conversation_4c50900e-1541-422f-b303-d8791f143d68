/* eslint-disable no-restricted-globals */
import React, { useEffect, useCallback, useState } from "react";
import { <PERSON><PERSON><PERSON>, Spinner } from "react-bootstrap";
import { Helmet } from "react-helmet";
import { IoIosClose } from "react-icons/io";
import "./styles/faq.scss";
import parse from "html-react-parser";
import { useHistory } from "react-router";
import * as sharepointService from "../services/sharepoint-service";
import { Divider } from "../components/common/Divider";

const SHAREPOINT_NOVA_PAGE_ID = 6;

const FAQ = () => {
  const history = useHistory();
  const [FAQContent, setFAQContent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const getFaqContent = useCallback(async () => {
    try {
      setIsLoading(true);
      const value = await sharepointService.getSharepointData(SHAREPOINT_NOVA_PAGE_ID);
      const richText = parse(value.data.data.CanvasContent1);
      setIsLoading(false);
      setFAQContent(richText as string);
    } catch (error) {
      setIsLoading(false);
      console.log("Something went wrong on retrieving sharepoint content for PARIS 2.0 FAQ", error);
    }
  }, [setFAQContent, setIsLoading]);

  useEffect(() => {
    getFaqContent();
  }, [getFaqContent]);

  const handleGoBack = () => window.open(window?.location?.href?.split('/faq')[0], '_self');

  return (
    <Container fluid style={{ paddingTop: '20px' }} className="faq-wrapper">
      <Helmet>
        <body className="faq-body" />
      </Helmet>
      <div className="faq-heading">
        <span className="faq-title">PARIS 2.0 - Frequently Asked Questions (FAQs)</span>
        <div role="button" aria-hidden="true" onClick={handleGoBack}>
          <IoIosClose size={52} />
        </div>
      </div>
      <Divider height="0.25" />
      <hr />
      {isLoading ? (
        <div className="faq-spinner">
          <Spinner animation="border" role="status" />
        </div>
      ) : FAQContent}
    </Container>
  );
};

export { FAQ };
