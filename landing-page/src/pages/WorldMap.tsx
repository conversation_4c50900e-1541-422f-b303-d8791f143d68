import React, { useState, useEffect } from 'react';
import './styles/worldmap.scss';
import { KeycloakProps } from '../types/keycloak';
import { Container } from 'react-bootstrap';
import { GuardWrapper } from '../components/common';
import WorldMapWidget from '../components/dashboard/WorldMapWidget';
import {
  VesselMarker,
  getVesselItineraries,
  getVesselListStratumData,
  getVesselV2List,
  getVesselPositionReportLatLon,
  OwnershipDetails,
  VesselStratumResponse,
} from '../services/vessel-service';
import moment from 'moment';
import _ from 'lodash';
import { getOwnershipVesselMap, getMissingOwnershipIds } from '../utils/world-map';

function WorldMap({
  keycloak,
  containerClassName = 'pt-3 pb-3',
  mapContainerStyle = {},
}: KeycloakProps & { containerClassName?: string; mapContainerStyle?: Record<string, string> }) {
  const [vesselList, setVesselList] = useState([]);
  const [vesselListRawData, setVesselListRawData] = useState([]);
  const [vesselListStratum, setVesselListStratum] = useState({});
  const [worldMapMarkerData, setWorldMapMarkerData] = useState<VesselMarker[]>([]);
  const [isLoadingVessel, setIsLoadingVessel] = useState<boolean>(true);
  const [isLoadingVesselStratumData, setIsLoadingVesselStratumData] = useState<boolean>(true);
  const [vesselItinerariesRawData, setVesselItinerariesRawData] = useState([]);

  const getVesselData = async () => {
    try {
      setIsLoadingVessel(true);
      const response = await getVesselV2List();
      setVesselListRawData(response?.data?.results);
      setIsLoadingVessel(false);
    } catch (err) {
      console.error('Unable to fetch vessel');
      setVesselListRawData([]);
      setIsLoadingVessel(false);
    }
  };

  const getAllVesselsStratumData = async () => {
    try {
      setIsLoadingVesselStratumData(true);
      const endTime = moment()
        .utc()
        .valueOf();
      const startTime = moment(endTime)
        .subtract(7, 'day')
        .valueOf();
      const { data } = await getVesselListStratumData(startTime, endTime);
      setVesselListStratum(data);
      setIsLoadingVesselStratumData(false);
    } catch (err) {
      console.error('Unable to fetch vessel stratum positions');
      setVesselListStratum([]);
      setIsLoadingVesselStratumData(false);
    }
  };

  const getVesselsPositionReportsData = async (vesselOwnershipIds: string) => {
    try {
      const { data } = await getVesselPositionReportLatLon(vesselOwnershipIds);
      return data;
    } catch (err) {
      console.error('Unable to fetch vessel position reports', err);
    }
  };

  const getOwnerVesselItineraries = async () => {
    try {
      const { data } = await getVesselItineraries();
      setVesselItinerariesRawData(data?.results);
    } catch (error) {
      console.log(
        `Something went wrong on loading vessel itineraries. Here is the full error ${error}`,
      );
      setVesselItinerariesRawData([]);
    }
  };

  useEffect(() => {
    getVesselData();
    getAllVesselsStratumData();
    getOwnerVesselItineraries();
  }, []);

  useEffect(() => {
    async function handleWorldMapMarkerData(
      vessels: OwnershipDetails[],
      positions: VesselStratumResponse[],
    ) {
      const ownershipVesselMap = getOwnershipVesselMap(vessels);
      const missingOwnershipIds = getMissingOwnershipIds(vessels, positions);
      const vesselList = vessels.map(vessel => {
        return {
          ...vessel,
          is_from_stratumFive: !missingOwnershipIds.includes(vessel.id) ? true : false,
        };
      });
      setVesselList(vesselList);
      let vesselPositions = { ...positions };
      if (missingOwnershipIds.length) {
        const positionReportsData = await getVesselsPositionReportsData(
          missingOwnershipIds.join(','),
        );
        const positionReportsDataWithVesselIds = {};
        if (positionReportsData) {
          for (const [ownershipId, value] of Object.entries(positionReportsData)) {
            const vesselId = ownershipVesselMap[ownershipId];
            positionReportsDataWithVesselIds[vesselId] = value;
          }
        }
        vesselPositions = { ...positions, ...positionReportsDataWithVesselIds };
      }
      const markerData: VesselMarker[] = vessels
        .map(vessel => {
          const vesselId = vessel.vessel.id;
          const vesselPosition = vesselPositions[vesselId];
          const vesselType = vessel.vessel_type;
          if (vesselPosition) {
            return {
              vessel_ownership_id: vessel.id,
              vessel_id: vesselId,
              vessel_short_code: vessel.vessel_short_code,
              vessel_type:
                vesselType.type.toLowerCase() === 'tanker' ? vesselType.type : vesselType.value,
              lat: _.toNumber(vesselPosition.lat),
              lng: _.toNumber(vesselPosition.lon),
              cog: vesselPosition?.cog ? _.toNumber(vesselPosition.cog) : 0, // angle
              isParking:
                _.toNumber(vesselPosition?.sog) > 0 || missingOwnershipIds.includes(vessel.id)
                  ? false
                  : true, // speed
            };
          }
        })
        .filter(Boolean);
      setWorldMapMarkerData(markerData);
    }
    if (!isLoadingVessel && !isLoadingVesselStratumData) {
      handleWorldMapMarkerData(vesselListRawData, vesselListStratum);
    }
  }, [isLoadingVessel, isLoadingVesselStratumData]);

  return (
    <GuardWrapper hasAccess={true}>
      <Container className={containerClassName}>
        <div>
          <WorldMapWidget
            containerStyle={mapContainerStyle}
            vesselList={vesselList}
            worldMapMarkerData={worldMapMarkerData}
            vesselItineraries={vesselItinerariesRawData}
            isLoadingVessel={isLoadingVessel}
            isWorldMapPage={true}
            mapZoom={2.68}
          />
        </div>
      </Container>
    </GuardWrapper>
  );
}

export { WorldMap };
