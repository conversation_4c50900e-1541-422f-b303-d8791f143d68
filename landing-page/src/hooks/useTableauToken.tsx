/* eslint-disable max-len */
import { useCallback, useEffect, useState } from 'react';
import * as tableauProxy from "../services/tableau-proxy";

const ERROR_STATE_TABLEAU_TOKEN = 'error';

interface HookProps {
  nums: number; // No of tokens needed
  hasTableauAccess: boolean;
}

function useTableauTokens({ nums, hasTableauAccess }: HookProps) {
  const [tableauTokens, setTableauTokens] = useState<(string | null)[]>([null]);

  const authenticateTableau = useCallback(async () => {
    try {
      const formDataCredentials = tableauProxy.generateTableauCredentials();
      const { data: { token } } = await tableauProxy.getTableauToken(formDataCredentials);
      return token;
    } catch (error) {
      console.log(`Something went wrong on authenticating with tableau proxy. Here is the full error ${error}`);
      return ERROR_STATE_TABLEAU_TOKEN;
    }
  }, []);

  const getListOfTableauToken = useCallback(async () => {
    const promises = [];
    for (let i = 0; i < nums; i += 1) {
      promises.push(authenticateTableau());
    }

    const barDashboardTokens = await Promise.all(promises.slice(0, 2)) as unknown as (string | null)[];
    setTableauTokens(barDashboardTokens);
  }, [authenticateTableau, nums]);

  useEffect(() => {
    if (hasTableauAccess) {
      getListOfTableauToken();
    }
  }, [getListOfTableauToken, hasTableauAccess]);

  return tableauTokens;
}

export { useTableauTokens };
