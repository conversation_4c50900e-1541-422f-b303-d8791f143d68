import { useEffect, useState } from 'react';

interface HookProps {
  hasAtleastOneSubscription: boolean;
  tableauTokens: (string | null)[];
}

function useDashboardLoader({ hasAtleastOneSubscription, tableauTokens }: HookProps) {
  const [isDashboardLoading, setDashboardLoading] = useState<boolean>(false);

  useEffect(() => {
    setDashboardLoading(true);
    if (hasAtleastOneSubscription) {
      setTimeout(() => {
        setDashboardLoading(false);
      }, 2500);
      return;
    }
    if (tableauTokens.includes('error')) {
      setDashboardLoading(false);
      return;
    }
    setDashboardLoading(false);
  }, [hasAtleastOneSubscription, tableauTokens]);

  return isDashboardLoading;
}

export { useDashboardLoader };
