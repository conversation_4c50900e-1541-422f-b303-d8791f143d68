/* eslint-disable max-len */
/* eslint-disable arrow-body-style */
import { AxiosResponse } from 'axios';
import { httpClient } from '../config/http-client';
import { createHeaders } from './user-service';
import { VesselOwned, VesselDetails } from '../types/vessel';
import { VesselQualship, VesselItinerary } from '../context/dashboard-context';

const { VESSEL_HOST, FILE_API_HOST } = process.env;

export interface VesselOwnedCurrent {
  id: number;
  name: string;
  vessel: {
    id: number;
    ref_id: number;
  };
  owner: {
    ship_party_id: number;
  };
  owner_start_date: string;
  owner_end_date: string;
}
interface VesselItineraryResponse {
  total: number;
  response: VesselItinerary[];
}

export interface VesselPositionReport {
  results: VesselPositionReportResult[];
  total: number;
}

export interface VesselPositionReportResult {
  created_at: string;
  updated_at: string;
  id: number;
  vessel_ownership_id: number;
  report_type: string;
  smt: string;
  gmt: string;
  report_json: ReportJson;
  name: string;
}

export interface ReportJson {
  general: General;
  consumption: Consumption;
  weather: Weather;
}

export interface General {
  latitude: string;
  longitude: string;
  next_country: string;
  next_port: string;
  eta: string;
  average_speed: number;
  ship_heading_at_report_time: number;
}

export interface Consumption {
  main_engine: MainEngine;
  diesel: Diesel;
}

export interface MainEngine {
  hfo: number;
}

export interface Diesel {
  mgo: number;
  hfo: number;
}

export interface Weather {
  current: Current;
}

export interface Current {
  wind_force: number;
  wind_direction: number;
  sea_height: number;
  swell_height: number;
  swell_direction: number;
}

export interface VesselMarker {
  vessel_ownership_id: number;
  vessel_id: number;
  vessel_short_code: string;
  vessel_type: string;
  lat: number;
  lng: number;
  cog: number;
  isParking: boolean;
}

export interface VesselTrack {
  vessel_stratum_id: number;
  vessel_id: number;
  lat: number;
  lng: number;
}

export interface VesselStratum {
  id: number;
  vessel_id: number;
  lat: string;
  lon: string;
  sog: string;
  cog: number;
  gpsTimestampEpoch?: string;
}
export interface VesselStratumResponse {
  [vessel_id: number]: VesselStratum;
}
/**
 *
 * @param shipPartyID ID of the Ship Party
 * @param shipPartyTypeID ID of the Ship Party Type. 1 = Owner, 2 = Registered Owner for example.
 *
 * @returns Returns a list of vessel the user currently owns.
 */
export const getVesselsCurrentlyOwned = async (
  shipPartyID: number,
  shipPartyTypeID: number,
  params = 'current=true',
): Promise<AxiosResponse<VesselOwnedCurrent[]>> => {
  return httpClient.get(
    `${VESSEL_HOST}/vessel-ownership/${shipPartyID}/${shipPartyTypeID}?${params}`,
    {
      headers: await createHeaders(),
    },
  );
};

export const getVessels = async (techGroup?: string | null) => {
  const sortParam = 'orderBy=vessel.created_at+desc';
  const limitParam = 'limit=5000';
  const filterParams = techGroup ? `techgroup=${techGroup}` : '';
  const fields = 'f=vessel.name&f=vessel.techgroup';
  const queryParams = [limitParam, sortParam, filterParams, fields].join('&');
  return httpClient.get(`${VESSEL_HOST}/query?${queryParams}`, {
    headers: await createHeaders(),
  });
};

interface VesselDetailsResponse {
  totalCount: number;
  vessels: VesselDetails[];
}

export const getActiveVessels = async (): Promise<AxiosResponse<VesselDetailsResponse>> => {
  const filterParams = 'status=active';
  const fields = 'f=vessel.id';
  const queryParams = [filterParams, fields].join('&');
  return httpClient.get(`${VESSEL_HOST}/query?${queryParams}`, {
    headers: await createHeaders(),
  });
};

interface CrewInjuryStatistics {
  total: number;
  response: {
    SHIPID: number;
    NAME: string;
    TAKEOVERDATE: string;
    HANDOVERDATE: string;
    INCIDENTDATE: string;
    INCIDENT_TIME: string;
    LTI: number;
    TRC: number;
    CREWCNT: number;
  }[];
}

export const getCrewInjuryStats = async (): Promise<AxiosResponse<CrewInjuryStatistics>> => {
  return httpClient.get(`${VESSEL_HOST}/query-vessel-crew-injury-statistics`, {
    headers: await createHeaders(),
  });
};

interface VesselQualshipResponse {
  total: number;
  response: VesselQualship[];
}

export const getVesselQualshipDetails = async (
  vesselID: number[],
): Promise<AxiosResponse<VesselQualshipResponse>> => {
  const queryParams = `vessel_id=${vesselID.join(',')}`;
  return httpClient.get(`${VESSEL_HOST}/query-qualship-21?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const downloadQualshipCertificate = async (path: string) => {
  const headers = await createHeaders();
  (headers as any).Accept = 'application/pdf';
  return httpClient.get(`${VESSEL_HOST}/download-qualship-certificate?docpath=${path}`, {
    headers,
    responseType: 'arraybuffer',
  });
};

export const downloadFile = async (path: string) => {
  const headers = await createHeaders();
  return httpClient.post(
    `${FILE_API_HOST}/request-download-urls`,
    {
      paths: [path],
    },
    {
      headers,
    },
  );
};

export const getPresignedDocument = async (url: string) => {
  return httpClient.get(url, {
    responseType: 'arraybuffer',
  });
};

export interface OwnershipDetails {
  id: number;
  name: string;
  vessel: {
    id: number;
  };
  vessel_short_code?: string;
  vessel_type?: {
    value: string;
    type: string;
  };
}

export interface OwnershipDetailsResponse {
  totalCount: number;
  results: OwnershipDetails[];
}

export const getOwnerships = async (
  filter = '',
): Promise<AxiosResponse<OwnershipDetailsResponse>> => {
  const statusParam = `status=active`;
  const sortParam = 'order=created_at+desc';
  const queryParams = [sortParam, statusParam, 'flatten=true'].join('&');
  return httpClient.get(`${VESSEL_HOST}/ownerships?${queryParams}${filter}`, {
    headers: await createHeaders(),
  });
};

export const getVesselV2List = async (): Promise<AxiosResponse<OwnershipDetailsResponse>> => {
  const statusParam = `status=active&status=pending_handover`;
  const sortParam = ''; // Add sort param when neccessary
  const queryParams = [sortParam, statusParam].join('&');
  return httpClient.get(`${VESSEL_HOST}/v2/ownerships?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const getVesselItineraries = async (): Promise<AxiosResponse<VesselItineraryResponse>> => {
  const limitParam = 'limit=5000';
  const offsetParam = 'offset=0';
  const queryParams = [limitParam, offsetParam].join('&');
  return httpClient.get(`${VESSEL_HOST}/itinerary?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const getVesselPositionReport = async (
  vessel_ownership_id: number,
): Promise<AxiosResponse<VesselPositionReport[]>> => {
  const pageSizeParam = 'pageSize=1';
  const pageIndexParam = 'pageIndex=0';
  const queryParams = [pageSizeParam, pageIndexParam].join('&');
  return httpClient.get(
    `${VESSEL_HOST}/report/technical/position/${vessel_ownership_id}?${queryParams}`,
    {
      headers: await createHeaders(),
    },
  );
};

export const getVesselPositionReportLatLon = async (
  vesselOwnershipIds: string,
  startDate?,
  endDate?,
): Promise<AxiosResponse<VesselPositionReport[]>> => {
  const queryParams = startDate && endDate ? `startDate=${startDate}&endDate=${endDate}` : '';
  const url = `${VESSEL_HOST}/report/technical/position-reports-lat-lon/${vesselOwnershipIds}${
    queryParams ? `?${queryParams}` : ''
  }`;
  return httpClient.get(url, {
    headers: await createHeaders(),
  });
};

export const getVesselListStratumData = async (
  startDateTime,
  endDateTime,
): Promise<AxiosResponse<VesselStratumResponse>> => {
  return httpClient.get(`${VESSEL_HOST}/stratum/${startDateTime}/${endDateTime}`, {
    headers: await createHeaders(),
  });
};

export const getVesselStratumData = async (vesselId: number, startDateTime, endDateTime) => {
  return httpClient.get(`${VESSEL_HOST}/${vesselId}/stratum/${startDateTime}/${endDateTime}`, {
    headers: await createHeaders(),
  });
};
