import { AxiosResponse } from 'axios';
import { httpClient } from '../config/http-client';
import { createHeaders } from './user-service';

const { REFERENCE_HOST } = process.env;

export const getCountries = async (): Promise<
  AxiosResponse<{
    countries: { alpha2_code: string; alpha3_code: string; value: string; numeric_code: string }[];
  }>
> => {
  return httpClient.get(`${REFERENCE_HOST}/countries`, {
    headers: await createHeaders(),
  });
};
