import * as spService from '../sharepoint-service';

export const getTermCondition = async () => ({
  data: {
    data: {
      "odata.metadata":
        "https://fleetship.sharepoint.com/sites/PARIS20-CMS/_api/$metadata#SP.ListData.SitePagesItems/@Element&$select=CanvasContent1,OData__UIVersionString",
      "odata.type": "SP.Data.SitePagesItem",
      "odata.id": "53b1b8fa-f82d-4401-8338-0b09bb899078",
      "odata.etag": '"45"',
      "odata.editLink":
        "Web/Lists(guid'0202c18e-fe12-4a62-a09d-aea678ce2178')/Items(2)",
      CanvasContent1:
        '<div><div data-sp-canvascontrol="" data-sp-canvasdataversion="1.0" data-sp-controldata="&#123;&quot;controlType&quot;&#58;4,&quot;id&quot;&#58;&quot;1212fc8d-dd6b-408a-8d5d-9f1cc787efbb&quot;,&quot;position&quot;&#58;&#123;&quot;controlIndex&quot;&#58;2,&quot;sectionIndex&quot;&#58;1,&quot;sectionFactor&quot;&#58;12,&quot;zoneIndex&quot;&#58;1,&quot;layoutIndex&quot;&#58;1&#125;,&quot;addedFromPersistedData&quot;&#58;true,&quot;emphasis&quot;&#58;&#123;&#125;&#125;"><div data-sp-rte=""><ul><li><p>Added 11 At Lorum ipum, our mission is to empower every person and every organization on the planet to achieve more. We are doing this by building an intelligent cloud, reinventing productivity and business processes and making computing more personal.&#160;</p><p>Content Copied from test update &#58;&#160;<a href="https&#58;//privacy.microsoft.com/en-gb/privacy" data-cke-saved-href="https&#58;//privacy.microsoft.com/en-gb/privacy">https&#58;//privacy.microsoft.com/en-gb/privacy</a></p></li></ul><p><br></p></div></div><div data-sp-canvascontrol="" data-sp-canvasdataversion="1.0" data-sp-controldata="&#123;&quot;controlType&quot;&#58;0,&quot;pageSettingsSlice&quot;&#58;&#123;&quot;isDefaultDescription&quot;&#58;true,&quot;isDefaultThumbnail&quot;&#58;true&#125;&#125;"></div></div>',
      OData__UIVersionString: "15.0",
    },
  },
});