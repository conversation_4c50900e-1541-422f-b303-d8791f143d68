import { VesselOwned } from "../../types/vessel"
import * as vesselService from '../vessel-service';

export const vesselOwnerDetail: VesselOwned[] = [
  {
    id: 1,
    name: "Test Vessel 1",
    date_of_takeover: null,
    ref_id: 10,
  },
  {
    id: 2,
    name: "Test Vessel 2",
    date_of_takeover: null,
    ref_id: 20,
  },
  {
    id: 3,
    name: "Test Vessel 3",
    date_of_takeover: null,
    ref_id: 30,
  },
]

export const vesselByTechGroup: VesselOwned[] = [
  {
    id: 1,
    name: "Tech Vessel 1",
    date_of_takeover: null,
    ref_id: 11,
  },
  {
    id: 2,
    name: "Tech Vessel 2",
    date_of_takeover: null,
    ref_id: 12,
  },
  {
    id: 3,
    name: "Tech Vessel 3",
    date_of_takeover: null,
    ref_id: 13,
  },
]

export const allVessels: VesselOwned[] = [
  {
    id: 1,
    name: "All Vessel 1",
    date_of_takeover: null,
    ref_id: 21,
  },
  {
    id: 2,
    name: "All Vessel 2",
    date_of_takeover: null,
    ref_id: 22,
  },
  {
    id: 3,
    name: "All Vessel 3",
    date_of_takeover: null,
    ref_id: 23,
  },
]

export const vesselTracksData: vesselService.VesselTrack[] = [
  {
    vessel_stratum_id: 6101060,
    vessel_id: 1404,
    lat: 0.0524767,
    lng: -124.5645433,
  },
  {
    vessel_stratum_id: 6101507,
    vessel_id: 1404,
    lat: 1.69092,
    lng: -129.8466533,
  },
  {
    vessel_stratum_id: 6101984,
    vessel_id: 1404,
    lat: 3.32153,
    lng: -135.10023,
  }, {
    vessel_stratum_id: 6102421,
    vessel_id: 1404,
    lat: 4.80898,
    lng: -139.9185417
  }, {
    vessel_stratum_id: 6533148,
    vessel_id: 1404,
    lat: 6.290805,
    lng: -144.389575
  },
];

export const vesselOwnershipDetailsList: vesselService.OwnershipDetails[] = [
  {
    id: 1,
    name: 'Test Vessel 1',
    vessel: {
      id: 1,
    },
    vessel_short_code: 'ABC',
    vessel_type: {
      id: 1,
      type: 'dry',
      value: 'container vessel',
    }
  },
  {
    id: 2,
    name: 'Test Vessel 2',
    vessel: {
      id: 2,
    },
    vessel_short_code: 'DEF',
    vessel_type: {
      id: 1,
      type: 'dry',
      value: 'bulk carrier',
    }
  },
  {
    id: 3,
    name: 'Test Vessel 3',
    vessel: {
      id: 3,
    },
    vessel_short_code: 'GHI',
    vessel_type: {
      id: 3,
      type: 'tanker',
      value: 'Oil cum Chemical Tanker'
    }
  }
];

export const mapMarkerData : vesselService.VesselMarker[] = [
    {
        vessel_ownership_id: 1,
        vessel_id: 1,
        vessel_short_code: 'ABC',
        vessel_type: 'container vessel',
        lat: 51.8687000000,
        lng: 3.4520417000,
        cog: 89,
        isParking: false,
    },
    {
        vessel_ownership_id: 2,
        vessel_id: 2,
        vessel_short_code: 'DEF',
        vessel_type: 'bulk carrier',
        lat: 8.8956500000,
        lng: -79.5004000000,
        cog: 274,
        isParking: false,
    },
    {
        vessel_ownership_id: 3,
        vessel_id: 3,
        vessel_short_code: 'GHI',
        vessel_type: 'tanker',
        lat: 25.1847100000,
        lng: 56.3732350000,
        cog: 251,
        isParking: false,
    }
];


export const vessselItinerariesData = [
  {
    estimated_arrival: '2023-12-03T21:00:00.000Z',
    country: 'Sri Lanka',
    port: 'Galle',
    id: 27776,
    vessel_id: 1,
    vessel_name: 'Chemical Challenger'
  },
  {
    estimated_arrival: '2023-12-24T16:00:00.000Z',
    country: 'China',
    port: 'Tianjin Xingang Pt',
    id: 472845,
    vessel_id: 2,
    vessel_name: 'Morning Crystal'
  },
  {
    estimated_arrival: '2023-12-24T16:00:00.000Z',
    country: 'United States',
    port: 'Martinez, CA',
    id: 472852,
    vessel_id: 3,
    vessel_name: 'Fairchem Blue Shark'
  },
];

export const allOwnedVesselStratumFiveData = {
  1: {
    id: 6365847,
    vessel_id: 1,
    lat: 51.8687000000,
    lon: 3.4520417000,
    gpsTimestampEpoch: 1699693144000,
    sog: 0.000,
    cog: 89
  },
  2: {
    id: 6631672,
    vessel_id: 2,
    lat: 8.8956500000,
    lon: -79.5004000000,
    gpsTimestampEpoch: 1700121588000,
    sog: 0.000,
    cog: 274
  },
  3: {
    id: 6365713,
    vessel_id: 3,
    lat: 25.1847100000,
    lon: 56.3732350000,
    gpsTimestampEpoch: 1699693337000,
    sog: 0.000,
    cog: 251
  }
};

export const getVesselsCurrentlyOwned = async () => ({
  data: vesselOwnerDetail,
})

export const getVessels = async (techGroup: string[]) => ({
  data: techGroup.length > 0 ? vesselByTechGroup : allVessels,
})

export const getVesselV2List = async () => ({
  data: {
    results: vesselOwnershipDetailsList,
    totalCount: 3,
  }
});

export const getVesselItineraries = async () => ({
  data: {
    results: vessselItinerariesData,
    total: 3,
  },
});

export const getVesselListStratumData = async () => ({
  data: allOwnedVesselStratumFiveData,
});

export const getCrewInjuryStats = async () => ({
  data: {
    response: [],
  },
});
