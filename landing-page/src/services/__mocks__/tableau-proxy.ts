/* eslint-disable arrow-body-style */
/* eslint-disable max-len */
import { AxiosResponse } from 'axios';
import { httpClient } from '../../config/http-client';
import { createHeaders } from './user-service';

const { TABLEAU_PROXY_HOST } = process.env;

interface TableauProxyResponse {
  token: string;
}

/**
 * Tableau has different authentication method aside from our keycloak
 *
 * see below:
 *
 * https://help.tableau.com/current/server/en-us/trusted_auth.htm
 *
 * @returns authentication details for tableau
 */
export const generateTableauCredentials = (): FormData => {
  const formData = new FormData();
  formData.append('username', 'devops');
  return formData;
};

/**
 * Tableau token is separate from our Keycloak authentication token.
 *
 * @returns authentication token for tableau
 */
export const getTableauToken = async (tableauCredentials: FormData) => {
  return {
    data: {
      token: '4Smvua68S2idkWLXKafk0A==:amMZb-pZPGm-zc3eTTRepNSn', // NOSONAR
    },
  };
};
