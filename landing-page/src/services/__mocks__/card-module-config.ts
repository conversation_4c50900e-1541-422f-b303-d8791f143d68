import { VesselGroup } from "../../types/card-types";

export const vesselGroups2: VesselGroup[] = [
  {
    id: 2,
    title: "",
    vessels: [
      { vessel_id: -1, name: "Unassigned" },
      { vessel_id: 3, name: "Critical" },
      { vessel_id: 2, name: "Special" },
      { vessel_id: 4, name: "Level 1 RA" }
    ]
  }
]


export const tableHeaders3 = [
  "Vessel",
  "Task Required",
  "Level of R.A.",
  "Status",
  "Action",
];

export const badgeColors1 = ["#d80e61", "#fbc02d", "#27a527"];
export const badgeColors2 = ["#d80e61", "#fbc02d", "#31a1bd"];

export const staticDataProps = {
  tabs: ["All", "Statutory", "Important", "Ancillary"],
  tableHeaders: tableHeaders3,
  badgeColors: badgeColors2,
};

export const visibleConfigProps = {
  IsiconRenderVisible: true,
  IsenLargeIconVisible: true,
  IsVesselSelectVisible: true,
  vesselSelectPosition: "after",
};

export const componentViewProps = {
  gridComponent: 'dashboard',
  defaultComponent: 'list',
};