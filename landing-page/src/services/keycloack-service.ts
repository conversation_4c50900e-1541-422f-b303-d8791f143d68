import { AxiosResponse } from 'axios';
import { httpClient } from '../config/http-client';
import { createHeaders } from './user-service';
const { KEYCLOAK_ADMIN_URL } = process.env;
const updateUserAttribute = async (userName: string, data: any) => {
  return httpClient.patch(`${KEYCLOAK_ADMIN_URL}/user/${userName}/update`, data, {
    headers: await createHeaders(),
  });
};
export { updateUserAttribute };
