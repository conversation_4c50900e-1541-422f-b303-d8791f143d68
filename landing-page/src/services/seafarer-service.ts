import { AxiosResponse } from 'axios';
import { httpClient } from '../config/http-client';
import { createHeaders } from './user-service';

const { SEAFARER_API_HOST } = process.env;

export interface ComplianceIssue {
  is_compliant: boolean;
  rank_group: string;
}

export interface ComplianceCategory {
  company: ComplianceIssue;
  vessel_type: ComplianceIssue;
  rank: ComplianceIssue;
}

export interface OcimfDetails {
  issues: ComplianceCategory[];
  badge: string | null;
}

export interface DocumentIssues {
  count: number;
  badge: string | null;
}

export interface ExperienceIssues {
  ranks: [string[]];
  badge: string | null;
}

export interface VesselCompliance {
  vessel_id: number;
  ocimf_sr: OcimfDetails;
  ocimf_jr: OcimfDetails;
  document_issues: DocumentIssues;
  experience_issues: ExperienceIssues;
  final_updated_at: number;
}

export const getPrecomputedCompliances = async (payload: {
  vessel_ids: number[];
}): Promise<AxiosResponse<VesselCompliance[]>> => {
  const headers = await createHeaders();
  const API_URL = `${SEAFARER_API_HOST}/ocimf/get-precomputed-compliances`;
  try {
    const response = await httpClient.post<VesselCompliance[]>(API_URL, payload, {
      headers,
    });
    return response;
  } catch (error) {
    console.error('Error fetching precomputed compliances:', error);
    throw error;
  }
};
