/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
import auth from "@paris2/auth";
import { httpClient } from "../config/http-client";

const { KEYCLOAK_ADMIN_URL } = process.env;

const { init, getToken } = auth;

const createHeaders = async () => {
  const token = await getToken();
  return {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    Authorization: `Bearer ${token}`,
  };
};

const updateUserAttribute = async (
  userName: string,
  data: any,
) => {
  console.log("UserName, data",userName,data);
  return httpClient.patch(`${KEYCLOAK_ADMIN_URL}/update-current-user`, data, {
    headers: await createHeaders(),
  });
};

export {
  init, getToken, createHeaders,
  updateUserAttribute,
};
