import axios from 'axios';
import * as xmljs from 'xml-js';
const { TABLEAU_PROXY_HOST, SITE_NAME } = process.env;

/**
 * GET RSS feed for site https://www.fleetship.com
 */
export const getSharepointData = async (itemId: number) => {
  return axios.get(`${TABLEAU_PROXY_HOST}/get-sp-data/${SITE_NAME}/Site Pages/${itemId}`);
};

/**
 * GET RSS feed for site https://www.fleetship.com
 */
export const getTermCondition = async ()=> {
  return axios.get(`${TABLEAU_PROXY_HOST}/get-sp-data/PARIS20-CMS/Site Pages/2`);
};