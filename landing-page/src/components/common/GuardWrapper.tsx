import React, { ReactElement } from "react";
import { ErrorPage } from "../../StyleGuide";

interface Props {
  hasAccess: boolean;
  children: ReactElement;
  fallback?: ReactElement | null;
  customMessage?: string | null;
}

const GuardWrapper = ({ hasAccess, children, fallback, customMessage }: Props) => {
  if (!hasAccess) {
    // Only render ErrorPage if its undefined. Null is considered valid here for hiding UI base on role
    return typeof fallback === 'undefined' ? <ErrorPage errorCode={403} customMessage={customMessage} /> : fallback;
  }
  return children;
};

export { GuardWrapper };
