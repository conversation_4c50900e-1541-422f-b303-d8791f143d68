import React from 'react';
import classNames from 'classnames';

import './styles/widget-badges.scss';

export type ColoredTileTheme = 'red' | 'green' | 'yellow' | 'blue' | 'blue-2' | 'gray';

export interface ColoredTileProps {
  text: string;
  theme: ColoredTileTheme;
  className?: string;
}

export const StatusBadge: React.FC<ColoredTileProps> = ({ text, theme, className }) => {
  return (
    <span className={classNames(`ra-colored-tile ra-colored-tile-${theme}`, className)}>{text}</span>
  );
};

export const CountBadge: React.FC<ColoredTileProps> = ({ text, theme, className }) => {
  return (
    <span className={classNames(`ra-rounded-badge ra-rounded-badge-${theme}`, className)}>{text}</span>
  );
};