import React from 'react';
import './styles/EmptyState.scss';

interface EmptyStateProps {
  text: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({ text }) => {
  return (
    <div className="empty-state-container">
      <svg
        width="48"
        height="49"
        viewBox="0 0 48 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M24 45.2324C35.598 45.2324 45 35.8304 45 24.2324C45 12.6344 35.598 3.23242 24 3.23242C12.402 3.23242 3 12.6344 3 24.2324C3 35.8304 12.402 45.2324 24 45.2324Z"
          stroke="#1F4A70"
          strokeWidth="3"
        />
        <path
          d="M22.406 33.7324V17.3688H25.5914V33.7324H22.406ZM24.0146 14.8439C23.4607 14.8439 22.9848 14.6593 22.5871 14.2899C22.1965 13.9135 22.0012 13.4661 22.0012 12.9476C22.0012 12.4221 22.1965 11.9746 22.5871 11.6053C22.9848 11.2289 23.4607 11.0407 24.0146 11.0407C24.5686 11.0407 25.0409 11.2289 25.4316 11.6053C25.8293 11.9746 26.0281 12.4221 26.0281 12.9476C26.0281 13.4661 25.8293 13.9135 25.4316 14.2899C25.0409 14.6593 24.5686 14.8439 24.0146 14.8439Z"
          fill="#1F4A70"
        />
      </svg>
      <p className="empty-state-text">{text}</p>
    </div>
  );
};

export default EmptyState;
