import React, { useEffect, useRef } from "react";
import { useGoogleMap } from "@react-google-maps/api";


interface MapControlProps {
    position: keyof typeof google.maps.ControlPosition;
}

  const CustomMapControl = (props: React.PropsWithChildren<MapControlProps>) => {
    const map = useGoogleMap();
    const ref = useRef<HTMLDivElement>(null);
    useEffect(() => {
      if (map && ref) {
        map.controls[window.google.maps.ControlPosition[props.position]].push(
          ref.current
        );
      }
    }, [map, ref]);
    return <div ref={ref} className="world-map-control">{props.children}</div>;
  };

  export default CustomMapControl;