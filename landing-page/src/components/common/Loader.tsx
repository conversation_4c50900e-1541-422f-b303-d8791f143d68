import React from "react";
import { Spinner } from "react-bootstrap";
import "./styles/loader.scss";

interface Props {
  fullScreen?: boolean;
  title?: string;
  containerStyle?: Record<string, unknown>;
}

const Loader = ({ fullScreen, title = "Loading...", containerStyle }: Props) => {
  const fullScreenClass = fullScreen && "full-screen-overlay";
  return (
    <div
      className={`load-overlay ${fullScreenClass}`}
      style={containerStyle}
      data-testid="full-screen-loader"
    >
      <Spinner animation="border" role="status">
        <span className="sr-only">{title}</span>
      </Spinner>
    </div>
  );
};

export { Loader };
