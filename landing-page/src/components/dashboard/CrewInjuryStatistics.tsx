import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import moment from "moment";

import { Card, Col, OverlayTrigger, Row, Tooltip, TooltipProps } from "react-bootstrap";
import { getCrewInjuryStats } from "src/services/vessel-service";
import Decimal from 'decimal.js';
interface CustomTooltipProps extends TooltipProps {
    tooltipText: string;
}

const CrewInjuryStatistics = (props, ref) => {
    const [crewInjuryStat, setCrewInjuryStat] = useState({
        totalShips: 0,
        noOfShipWithNoIncident: 0,
        incidentFreeExposureHour: 0,
        incidentFreeDays: 0,
        ltiIncidents: 0,
        dateOfLastLtiIncident: '',
        ltif: '',
        trcf: '',
    });
    const crewStatisticRef = useRef(null);
    useImperativeHandle(ref, () => ({
        get crewStatistics() {
            return crewStatisticRef;
        }
    }));
    useEffect(() => {
        (async () => {
            try {
                const { data } = await getCrewInjuryStats();

                let incidentFreeHours = new Decimal(0);
                let shipWithNoIncident = 0;
                let lti = 0;
                let trc = 0;
                let latestIncidentDate = new Date(0);
                let updatedIncidentDate: string | number | Date | null = null;

                data.response.forEach((ship) => {
                    let currentDate = new Date();
                    const currYear = (new Date()).getFullYear();
                    let startYear = new Date(`31-Dec-${currYear-1}`);

                    if (ship.INCIDENTDATE) {
                      updatedIncidentDate = new Date(ship.INCIDENTDATE).setUTCHours(new Date(ship.INCIDENTDATE).getUTCHours() + 8);
                      const currShipIncidentDate = new Date(updatedIncidentDate);
                      if (currShipIncidentDate > latestIncidentDate) {
                          latestIncidentDate = currShipIncidentDate;
                      }
                    }

                    if (ship.HANDOVERDATE && new Date(ship.HANDOVERDATE) < currentDate) {
                      let updatedHandoverDate = new Date(ship.HANDOVERDATE).setUTCHours(new Date(ship.HANDOVERDATE).getUTCHours() + 8);
                        currentDate = new Date(updatedHandoverDate);
                    }

                    if (ship.TAKEOVERDATE && startYear < new Date(ship.TAKEOVERDATE)) {
                      let updatedTakeoverDate = new Date(ship.TAKEOVERDATE).setUTCHours(new Date(ship.TAKEOVERDATE).getUTCHours() + 8);
                      startYear = new Date(updatedTakeoverDate);  
                    }

                    if (ship.INCIDENTDATE == null) {
                        shipWithNoIncident += 1;
                    }

                    const shipIncidentDate = ship.INCIDENTDATE ? new Date(ship.INCIDENTDATE) : startYear;

                    let manDays = new Decimal(currentDate.getTime()).minus(shipIncidentDate.getTime()).div(1000 * 60 * 60 * 24);
                    let incidentFreeTime = new Decimal(currentDate.getTime()).minus(shipIncidentDate.getTime()).mod(1000 * 60 * 60 * 24);
                    manDays = manDays.greaterThan(0) ? manDays.minus(1) : manDays;
                    let dayOfYear = new Decimal(manDays).plus(new Decimal(incidentFreeTime).dividedBy(new Decimal(10).pow(12)));
                    let exposureHours = new Decimal(ship.CREWCNT).times(dayOfYear).times(24);
                    incidentFreeHours = new Decimal(incidentFreeHours).plus(exposureHours);

                    lti += ship.LTI;
                    trc += ship.TRC;
                });

                const ltif = new Decimal(lti).times(1000000).dividedBy(incidentFreeHours);
                const trcf = new Decimal(trc).times(1000000).dividedBy(incidentFreeHours);

                const crewInjuryEst = {
                    totalShips: data.response.length,
                    noOfShipWithNoIncident: shipWithNoIncident,
                    incidentFreeExposureHour: Math.floor(incidentFreeHours),
                    incidentFreeDays: Math.floor(incidentFreeHours / 24),
                    ltiIncidents: lti,
                    dateOfLastLtiIncident: moment(latestIncidentDate).format('DD MMM YYYY'),
                    ltif: ltif.toFixed(2),
                    trcf: trcf.toFixed(2),
                };
                setCrewInjuryStat(crewInjuryEst);
            } catch (err) {
                console.log(err);
            }
        })();
    }, []);
    const RenderTooltip = (props: CustomTooltipProps) => {
        const { tooltipText } = props;
        return (
            <Tooltip id="button-tooltip" {...props}>
                {tooltipText}
            </Tooltip>
        );
    };
    const CrewInjuryStatistics = () => (
        <Row className="crew-injury-statistics text-center mt-xl-2 mt-lg-2 mt-md-2 pl-2" noGutters>
            <Col xs={12} className="p-2 mb-xl-2 mb-lg-1 mb-md-1">
                <div className="stat-label">Number of Ships with “NO” Incident </div>
                <div className="stat-number">{crewInjuryStat.noOfShipWithNoIncident.toLocaleString()}</div>
            </Col>
            <Col xs={12} className="p-2 mb-xl-2 mb-lg-1 mb-md-1">
                <div className="stat-label">Incident-free Exposure Hour  </div>
                <div className="stat-number">{crewInjuryStat.incidentFreeExposureHour.toLocaleString()}</div>
            </Col>
            <Col xs={12} className="p-2 mb-xl-2 mb-lg-1 mb-md-1">
                <div className="stat-label" style={{ whiteSpace: 'pre-line' }}>{`Incident-free \n Days `}</div>
                <div className="stat-number">{crewInjuryStat.incidentFreeDays.toLocaleString()}</div>
            </Col>
            <Col xs={12} className="p-2 mb-xl-2 mb-lg-1 mb-md-1">
                <OverlayTrigger
                    placement="top"
                    delay={{ hide: 400 }}
                    overlay={(props: any) => RenderTooltip({ ...props, tooltipText: 'Lost Time Injury' })}
                >
                    <div className="stat-label">LTI Incident(s) </div>
                </OverlayTrigger>
                <div className="stat-number">{crewInjuryStat.ltiIncidents.toLocaleString()}</div>
            </Col>
            <Col xs={12} className="p-2 mb-xl-2 mb-lg-1 mb-md-1">
                <div className="stat-label">Last LTI Incident</div>
                <div className="stat-number">{crewInjuryStat.dateOfLastLtiIncident || '01 Jan 2021'}</div>
            </Col>
            <Col xs={12} className="p-2 mb-xl-2 mb-lg-1 mb-md-1">
                <Row noGutters>
                    <Col>
                        <OverlayTrigger
                            placement="top"
                            delay={{ hide: 400 }}
                            overlay={(props: any) => RenderTooltip({ ...props, tooltipText: 'Lost Time Injury Frequency' })}
                        >
                            <div>LTIF</div>
                        </OverlayTrigger>
                        <div className="stat-number">{crewInjuryStat.ltif.toLocaleString() || '0.00'}</div>
                    </Col>
                    <Col>
                        <OverlayTrigger
                            placement="top"
                            delay={{ hide: 400 }}
                            overlay={(props: any) => RenderTooltip({ ...props, tooltipText: 'Total Recordable Case Frequency' })}
                        >
                            <div>TRCF</div>
                        </OverlayTrigger>
                        <div className="stat-number">{crewInjuryStat.trcf.toLocaleString() || '0.00'}</div>
                    </Col>
                </Row>
            </Col>
        </Row>
    );
    return (
        <div className='report-container widget-border card'>
            <Card.Body className="" ref={crewStatisticRef} id="crew-injury-stats-block">
                <p className="report-title">FLEET Crew Statistics</p>
                {
                    CrewInjuryStatistics()
                }

            </Card.Body>
        </div>

    );
}
export default forwardRef(CrewInjuryStatistics);