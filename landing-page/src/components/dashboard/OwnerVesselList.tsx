import React, { useContext, useEffect, useState, useMemo } from 'react';
import _ from 'lodash';
import { Col, Row, Card, Form, Spinner } from 'react-bootstrap';
import { useDebouncedCallback } from 'use-debounce';
import OwnerVesselTable from './OwnerVesselTable';
import { VesselOwned } from '../../types/vessel';
import { useHistory } from 'react-router';
import { GlobalContext } from '../../context/dashboard-context';
import { VesselColumns } from '../../constants/table-widgets-metadata';
import { Keycloak } from '../../types/keycloak';
import { hasOfrAccess } from '../../utils/roles';

interface OwnerVesselListProps {
  vesselList: VesselOwned[];
  isLoadingVessel: boolean;
  keycloak: Keycloak;
  shipPartyId?: number | null;
  shipPartyType?: string;
}

const OwnerVesselList = (props: OwnerVesselListProps) => {
  const { vesselList = [], isLoadingVessel = false, shipPartyId, shipPartyType, keycloak } = props;
  const [filteredVesselList, setFilteredVesselList] = useState<VesselOwned[]>([]);
  const [searchedVesselName, setSearchedVesselName] = useState<string | null>(null);
  const { PARIS_ONE_HOST } = process.env;
  const history = useHistory();
  const { ga4EventTrigger } = useContext(GlobalContext);
  const { tokenParsed } = keycloak;
  const isOfrEnabled = hasOfrAccess(keycloak);
  const ship_party_id = tokenParsed?.ship_party_id ?? 0;
  const vesselsColumns = useMemo(() => VesselColumns(ship_party_id), [ship_party_id]);

  const handleVesselNameChange = useDebouncedCallback(value => {
    ga4EventTrigger && ga4EventTrigger('Landing Vessels  – List', value, 'Filter of Vessel');
    setSearchedVesselName(value.toLowerCase());
  }, 200);

  useEffect(() => {
    if (searchedVesselName)
      setFilteredVesselList(
        vesselList.filter(
          vessel => vessel.name && vessel.name.toLowerCase().includes(searchedVesselName),
        ),
      );
    else setFilteredVesselList(vesselList);
  }, [vesselList, searchedVesselName]);

  const visitVessel = (vessel: VesselOwned) => {
    if (ga4EventTrigger) ga4EventTrigger('Vessels', _.toString(vessel.name));
    if (shipPartyId) {
      const link = '/FMLLoginKeycloak?targeturl=/fml';
      window.open(
        `${PARIS_ONE_HOST}/fml${link}/PARIS?display=ship&shipid=${vessel.ref_id}`,
        '_blank',
      );
    } else history.push(`/vessel/ownership/details/${vessel.id}`);
  };

  return (
    <>
      <Card body className="widget-border owner-report-container">
        <Row>
          <Col xs={12} md={4} className="owner-title">
            <p className="report-title">My Vessels</p>
          </Col>
          <Col xs={12} md={8} className="no-print">
            <Form.Control
              data-testid="search-vessel-dropdown"
              type="text"
              name="vessel_name"
              placeholder="Filter by Vessel Name"
              onChange={e => handleVesselNameChange(e.target.value)}
            />
          </Col>
        </Row>
        <Row>
          {isLoadingVessel ? (
            <div className="vessel-loading-wrapper">
              <Spinner animation="border" role="status" />
            </div>
          ) : (
            <OwnerVesselTable
              vessels={filteredVesselList}
              columns={vesselsColumns}
              isOfrEnabled={isOfrEnabled}
            />
          )}
        </Row>
      </Card>
    </>
  );
};

export default OwnerVesselList;
