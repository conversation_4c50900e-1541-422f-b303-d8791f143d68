.three-dots-dropdown {
    .dropdown-toggle-no-caret {
        cursor: pointer;
        width: 20px;
        height: 20px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        &::after {
            display: none; // Remove the default caret
        }

        &:hover,
        &:focus,
        &:active {
            background-color: #edf3f7;
            color: #1c1f4a;
        }
    }

    // Apply styling when dropdown is open
    &.show .dropdown-toggle-no-caret {
        background-color: #edf3f7;
        color: #1c1f4a;
    }

    .dropdown-menu {
        min-width: 124px;
        border-radius: 4px;
        border: 1px solid #cccccc;
        padding: 8px;

        .dropdown-item {
            font-size: 14px;
            padding: 4px 8px;

            &:hover,
            &:focus {
                background-color: #f8f9fa;
                color: #1c1f4a;
            }
        }
    }
}