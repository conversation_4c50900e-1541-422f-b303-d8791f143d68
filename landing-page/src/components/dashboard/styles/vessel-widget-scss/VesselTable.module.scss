@use "./variables" as *;

.ra-tableContainer {
  position: relative;
  overflow-y: auto;
  overflow-x: auto;
  max-height: 360px;
}

.ra-table {
  font-size: 14px;
  border-collapse: collapse;

  .ra-tableHeader {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 10;
    white-space: nowrap;

    tr {
      font-size: 1.0rem !important;
      color: #000000;
    }

    th {
      padding: 0.5rem 0;
      font-weight: 600;
      user-select: none;
      width: 140px;
      text-wrap: auto;
    }
  }

  .ra-tableRow {
    border-top: 1px solid #e5e7eb;
    white-space: nowrap;

    td {
      padding: 0.5rem 0;
      width: 140px;
      text-wrap: auto;
    }
  }
}

// .ra-tableHeader th:nth-child(n+3),
.ra-tableRow td:nth-child(n+3) {
  text-align: center;
}

.ra-tableHeader th:nth-child(n+3) {
  text-align: center !important;
  justify-content: center;
}


.ra-headerContent {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.ra-sortIcon {
  &.neutral {
    color: #a1a1aa;
  }
  &.active {
    color: $color-fleet-blue;
  }
}

.ra-actionHeader {
  min-width: 60px !important;
  width: 60px !important;
  max-width: 60px !important;
  &.nonSortable {
    cursor: default;
  }
}

.ra-textLeft {
  text-align: left;

  .ra-headerContent {
    justify-content: flex-start;
  }
}
.ra-vesselName-header-cell {
  text-align: left;
  min-width: 140px !important;
  width: 140px !important;
  max-width: 140px !important;

  .ra-headerContent {
    justify-content: flex-start;
  }
}

.ra-task-header-cell {
  text-align: left;
  min-width: 292px !important;
  max-width: 292px !important;
  width: 292px !important;

  .ra-headerContent {
    justify-content: flex-start;
  }
}
.ra-level-header-cell {
  min-width: 160px !important;
  max-width: 160px !important;
  width: 160px !important;
}
.ra-action-header-cell {
  min-width: 60px !important;
  max-width: 60px !important;
  width: 60px !important;
}
.ra-vesselNameCell {
  font-weight: 600;
  text-align: left !important;

  .ra-vesselNameButton {
    color: $color-fleet-blue;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    font-weight: 600 !important;
    text-decoration: underline;
    text-underline-offset: 2px;
    outline: none;
    text-align: left;
  }
}

.ra-vesselSecondColumnEntry {
  padding: 0.5rem 0;
}

.ra-badge {
  color: var(--badge-text-color, white);
  background-color: var(--badge-bg-color, #808080);
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem !important;
  display: inline-block;
}

.ra-badge-gray {
  background-color: #e0e0e0;
  color: #000;
}

.ra-badge-yellow {
  background-color: #fff9e8;
  color: #f08100;
}

.ra-badge-red {
  background-color: #faf2f5;
  color: #c82333;
}

.ra-statusBadge {
  display: inline-block;
  padding: 4px 10px !important;
  font-size: 12px;
  line-height: 1.2;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 5px;
  background-color: rgba(var(--status-rgb, 128, 128, 128));
  color: rgba(var(--status-rgb-text, 128, 128, 128));
}

.ra-status-critical {
  background-color: #FAF2F5;
  color: #C82333;
}

.ra-status-special {
  background-color: #FFF9E8;
  color: #F08100;
}

.ra-status-unassigned {
  background-color: #E5F4F8;
  color: #1F4A70;
}

.ra-status-default {
  background-color: #E0E0E0;
  color: #000;
}

.ra-approvedBadge {
  background-color: #e8f5e9;
  color: #4caf50;
}

.ra-approvedWithConditionBadge {
  background-color: #f1f8e9;
  color: #8bc34a;
}

.ra-rejectedBadge {
  background-color: #ffebee;
  color: #f44336;
}

.ra-emailCell {
  text-align: center;
}

.ra-emailButtonWrapper {
  position: relative;
  display: inline-block;

  .ra-emailButton {
    color: $color-fleet-blue;
    cursor: pointer;
    padding: 0.25rem 0;
    background: none;
    border: none;
    outline: none;
  }

  .ra-tooltip {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 12px;
    background: black;
    color: white;
    font-weight: 300;
    font-size: 14px !important;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s;
    pointer-events: none;
    z-index: 10;

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 6px solid transparent;
      border-top-color: black;
    }
  }

  &:hover .ra-tooltip {
    opacity: 1;
  }
}

.ra-spinnerContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
}

.ra-statusCell {
  text-align: center;
  padding: 4rem 0;
  font-size: 1rem;
  color: #6b7280;
  height: 200px;
}

@media (max-width: 768px) {
  .ra-tableContainer {
    max-height: calc(100vh - 200px) !important;
    overflow-x: auto;
  }
  .ra-table {
    .ra-tableHeader th,
    .ra-tableRow td {
      padding: 0.5rem 0.25rem;
    }
    .ra-tableHeader tr {
      font-size: 0.75rem;
    }
    .ra-tableRow .ra-badge,
    .ra-statusBadge {
      padding: 0.15rem 0.5rem;
      font-size: 0.65rem !important;
    }
  }
}
