.table-striped tbody tr:nth-of-type(odd) {
  background: #edf3f7 0% 0% no-repeat padding-box;
}

.table-header-container {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  border-spacing: 1.25rem;
}

.qualship-small-table {
  .table-responsive {
    max-height: 20rem;
  }
}

.column-header {
  font: normal normal normal 14px/34px Inter, sans-serif;
  letter-spacing: 0px;
  color: #333333;
}

.vessel-column-header {
  text-align: left;
  padding-left: 1.25rem !important;
}

.certificate-column-header {
  text-align: right;
  padding-right: 1.25rem !important;
}

.qualship-vessel-text {
  text-decoration: underline;
  font: normal normal bold 14px/34px Inter, sans-serif;
  letter-spacing: 0px;
  color: #1f4a70;
  text-align: left;
  padding-left: 1.25rem;
}

e-zero-col {
  text-align: center !important;
}

.e-zero-text {
  font: normal normal bold 14px/34px Inter, sans-serif;
  letter-spacing: 0px;
  color: #333333;
  text-align: center;
}

.view-certificate-text {
  text-decoration: underline;
  font: normal normal bold 14px/34px Inter, sans-serif;
  letter-spacing: 0px;
  color: #1f4a70;
  text-align: right;
  padding-right: 1.25rem;
}

.widget-statistics-title {
  text-align: center;
  font: normal normal bold 14px/17px Inter, sans-serif;
  letter-spacing: 0px;
  color: #1f4a70;
  align-content: center;
}

.more-info-block {
  text-decoration: underline;
  font: normal normal bold 14px/34px Inter, sans-serif;
  letter-spacing: 0px;
  color: #1f4a70;
  text-align: center;
  justify-content: center;
  display: flex;
}
