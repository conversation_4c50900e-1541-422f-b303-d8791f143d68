import React, { ReactElement } from 'react';
import { Icon } from '../../StyleGuide';
import "./styles/alert-guide.scss";

interface Props {
  text?: string | null;
  caption?: string,
  icon?: ReactElement;
}

const AlertGuide = ({ text = 'Coming soon', caption, icon }: Props) => {
  if (icon && caption) {
    return (
      <div className="alert-guide-wrapper">
        {icon}
        <span className="alert-guide-text">{caption}</span>
      </div>
    );
  }
  return (
    <div className="report-widget-content">
      <Icon icon="timetable" size={26} className="text-primary" />
      <span className="coming-soon-text">{text}</span>
    </div>
  );
};

export default AlertGuide;
