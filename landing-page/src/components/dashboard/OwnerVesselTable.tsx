import React , { useRef } from 'react';
import { VesselOwned } from '../../types/vessel';
import { noResultsFound } from "./VesselTable";
import { TableWidget } from "./TableWidget";

interface OwnerVesselTableProps {
  vessels: VesselOwned[];
  columns: any[];
  isOfrEnabled: boolean;
};

const OwnerVesselTable = (props: OwnerVesselTableProps) => {
  const { vessels, columns, isOfrEnabled } = props;
    const containerRef = useRef(null);
  return (
    <div className="vessel-table-wrapper vessel-container">
      <div className="vessel-table" ref={containerRef}>
        {vessels && vessels.length === 0 ?
          noResultsFound() :
          <TableWidget columns={columns} data={vessels} isOfrEnabled={isOfrEnabled} from='VesselList' containerRef={containerRef}/>
        }
      </div>
    </div>
  );
};

export default OwnerVesselTable;
