/* eslint-disable max-len */
/* eslint-disable no-underscore-dangle */
import React from 'react';
import { noResultsFound } from './VesselTable';
import { TableWidget } from './TableWidget';

const OwnerVesselItinerariesTable = ({ vesselItineraries, columns }) => {
  return (
    <div className="vessel-table-wrapper">
      <div className="vessel-table itineraries-table">
        {vesselItineraries && vesselItineraries.length === 0 ? (
          noResultsFound()
        ) : (
          <TableWidget columns={columns} data={vesselItineraries} />
        )}
      </div>
    </div>
  );
};

export default OwnerVesselItinerariesTable;
