/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import { OverlayTrigger, Tooltip, Popover } from 'react-bootstrap';
import { FiAlertCircle } from 'react-icons/fi';
import { uniq } from 'lodash';
import {
  getRanksForTooltip,
  getTypeOfCompliance,
  getOcimfPillColor,
  RANKS_MAPPING,
} from '../../utils/ocimf';
import { Icon } from '../../StyleGuide';
import { VesselOwned } from '../../types/vessel';
import { ComplianceCategory, VesselCompliance } from '../../services/seafarer-service';

const getOcimfTooltipContent = (row: ComplianceCategory, key: string) => {
  if (!row?.[key]?.is_compliant) {
    const rankText = getRanksForTooltip(row?.[key]?.rank_group);
    const typeOfComplianceText = getTypeOfCompliance(key);
    return (
      <div className="ocimf-tooltip-single-content-wrapper">
        <div className="ocimf-tooltip-header">{rankText}</div>
        <div className="ocimf-tooltip-text">{typeOfComplianceText}</div>
      </div>
    );
  }
  return row?.[key]?.is_compliant ? null : 'OCIMF Compliant';
};

const renderOcimfPill = (
  issues: ComplianceCategory[],
  badgeColor: string,
  label: string,
  count: number,
  vessel: VesselOwned,
  visitCrewList: Function,
  ga4EventTrigger: Function,
) =>
  issues?.length > 0 && (
    <OverlayTrigger
      placement="bottom"
      overlay={
        <Tooltip id={`tooltip-${label}`}>
          <div className="ocimf-tooltip-wrapper">
            {uniq(
              issues?.map((issue) =>
                Object.keys(issue)?.map((key) => getOcimfTooltipContent(issue, key)),
              ),
            ).filter(Boolean)}
          </div>
        </Tooltip>
      }
    >
      <div
        className="ocimf-status-pill"
        style={getOcimfPillColor(badgeColor)}
        onClick={() => {
          if (ga4EventTrigger) {
            let badgeType: string;
            if (badgeColor === 'red') {
              badgeType = 'OCIMF (senior ranks)';
            } else if (badgeColor === 'yellow') {
              badgeType = 'OCIMF';
            } else {
              badgeType = label;
            }
            ga4EventTrigger('Vessels Widget - Badge', badgeType, 'Link to Crew List');
          }
          visitCrewList(vessel, true, true);
        }}
        onKeyDown={() => {}}
      >
        {label}: {count}
      </div>
    </OverlayTrigger>
  );

const renderDocumentIssuePill = (
  count: number,
  vessel: VesselOwned,
  visitCrewList: Function,
  ga4EventTrigger: Function,
) =>
  count > 0 && (
    <OverlayTrigger
      placement="bottom"
      overlay={
        <Tooltip id="tooltip-documents">
          <div className="ocimf-tooltip-wrapper">
            <strong>{`${count} Crew`}</strong>
            Documents not verified by the Flag Office
          </div>
        </Tooltip>
      }
    >
      <div
        className="ocimf-status-pill"
        style={getOcimfPillColor('red')}
        onClick={() => {
          if (ga4EventTrigger)
            ga4EventTrigger('Vessels Widget - Badge', 'Doc', 'Link to Crew List');
          visitCrewList(vessel, true, true);
        }}
        onKeyDown={() => {}}
      >
        Doc: {count}
      </div>
    </OverlayTrigger>
  );

const renderExperienceIssuePill = (
  issues: string[],
  badgeColor: string | null,
  vessel: VesselOwned,
  visitCrewList: Function,
  ga4EventTrigger: Function,
) =>
  issues?.length > 0 && (
    <OverlayTrigger
      placement="bottom"
      overlay={
        <Tooltip id="tooltip-experience">
          <div className="ocimf-tooltip-wrapper">
            <strong>{`${issues.map((rank: string) => RANKS_MAPPING[rank]).join(', ')}`}</strong>
            "Exp. in Company" is less than 6 months
          </div>
        </Tooltip>
      }
    >
      <div
        className="ocimf-status-pill"
        style={getOcimfPillColor(badgeColor as string)}
        onClick={() => {
          if (ga4EventTrigger)
            ga4EventTrigger('Vessels Widget - Badge', 'Exp', 'Link to Crew List');
          visitCrewList(vessel, true, true);
        }}
        onKeyDown={() => {}}
      >
        Exp: {issues.length}
      </div>
    </OverlayTrigger>
  );

const getOcimfCount = (issues: ComplianceCategory[]) =>
  issues?.flatMap((issue) => Object.values(issue)).filter((item) => !item.is_compliant).length;

const ocimfPill = (
  ocimf: VesselCompliance,
  vessel: VesselOwned,
  visitCrewList: Function,
  ga4EventTrigger: Function,
) => {
  const { ocimf_sr, ocimf_jr, document_issues, experience_issues } = ocimf;
  return (
    <div className="ocimf-status-pills-wrapper">
      {renderOcimfPill(
        ocimf_sr?.issues,
        ocimf_sr?.badge as string,
        'OCIMF',
        getOcimfCount(ocimf_sr?.issues),
        vessel,
        visitCrewList,
        ga4EventTrigger,
      )}
      {renderOcimfPill(
        ocimf_jr?.issues,
        ocimf_jr?.badge as string,
        'OCIMF',
        getOcimfCount(ocimf_jr?.issues),
        vessel,
        visitCrewList,
        ga4EventTrigger,
      )}
      {renderDocumentIssuePill(document_issues?.count, vessel, visitCrewList, ga4EventTrigger)}
      {renderExperienceIssuePill(
        experience_issues?.ranks?.[0],
        experience_issues?.badge,
        vessel,
        visitCrewList,
        ga4EventTrigger,
      )}
    </div>
  );
};

export const noResultsFound = () => (
  <div className="text-center">
    <FiAlertCircle className="no-results-found" />
    <p className="coming-soon-text">No Results Found</p>
  </div>
);

const moreOptions = (
  vessel: VesselOwned,
  visitVessel: Function,
  visitCrewList: Function,
  visitEuEts: Function,
  setActivePopoverVessel: Function,
  ga4EventTrigger: (
    category: string,
    label: string,
    action?: string | undefined,
  ) => void | undefined,
  shouldVisitCrewListWithOcimf = false,
) => (
  <div style={{ textAlign: 'center', cursor: 'auto' }} aria-hidden="true">
    <OverlayTrigger
      rootClose
      placement="bottom"
      trigger="click"
      overlay={
        <Popover id="vessel-table-list-popover">
          <Popover.Content>
            <ul className="vessel-table-list-popover">
              <li
                data-testid="vessel-details"
                onClick={() => visitVessel(vessel)}
                onKeyDown={() => {}}
              >
                Vessel Details
              </li>
              <li
                data-testid="crew-list"
                onClick={() => visitCrewList(vessel, shouldVisitCrewListWithOcimf)}
                onKeyDown={() => {}}
              >
                Crew List
              </li>
              <li
                data-testid="eu-ets-report"
                onClick={() => visitEuEts(vessel)}
                onKeyDown={() => {}}
              >
                EU-ETS Report
              </li>
            </ul>
          </Popover.Content>
        </Popover>
      }
    >
      <div>
        <Icon
          icon="more"
          size={20}
          className="default"
          style={{ cursor: 'pointer' }}
          data-testid={`more-options-${vessel.id}`}
          onClick={() => {
            if (ga4EventTrigger)
              ga4EventTrigger('Vessels Widget - More Menu', 'Click', 'Open Menu');
            setActivePopoverVessel(true);
          }}
        />
      </div>
    </OverlayTrigger>
  </div>
);

const VesselTable = ({
  compliantVessels,
  nonCompliantVessels,
  isPreComputeApiCalled,
  visitVessel,
  visitCrewList,
  visitEuEts,
  ga4EventTrigger = () => {},
}: {
  compliantVessels: VesselOwned[];
  nonCompliantVessels: (VesselOwned & { ocimf?: VesselCompliance })[];
  isPreComputeApiCalled: boolean;
  visitVessel: Function;
  visitCrewList: Function;
  visitEuEts: Function;
  ga4EventTrigger:
    | ((category: string, label: string, action?: string | undefined) => void)
    | undefined;
}) => {
  const [activePopoverVessel, setActivePopoverVessel] = useState(false);
  useEffect(() => {
    const vesselTableWrapper = document.querySelector('.vessel-table-wrapper');
    const popover = document.getElementById('vessel-table-list-popover');
    if (!vesselTableWrapper || !popover) return;
    const handleScroll = () => {
      popover.style.display = 'none';
      setActivePopoverVessel(false);
    };
    vesselTableWrapper.addEventListener('scroll', handleScroll);
    return () => {
      vesselTableWrapper.removeEventListener('scroll', handleScroll);
    };
  }, [activePopoverVessel]);
  return (
    <div className="vessel-table-home">
      {isPreComputeApiCalled && !nonCompliantVessels.length && !compliantVessels.length ? (
        noResultsFound()
      ) : (
        <div className="vessel-table-wrapper">
          {!!nonCompliantVessels?.length && (
            <>
              <div className="vessel-table-subheader">
                <strong>{nonCompliantVessels.length}</strong> Non-Compliant OCIMF Tankers
              </div>
              {[...nonCompliantVessels]
                ?.sort((a, b) => a.name.localeCompare(b.name))
                ?.map((vessel) => (
                  <div key={vessel.id} className="table-vessel-row">
                    <div
                      className="table-vessel-name"
                      onClick={() => {
                        if (ga4EventTrigger)
                          ga4EventTrigger(
                            'Vessels Widget - Vessel Link',
                            vessel.name,
                            'Link to Vessel Details',
                          );
                        visitVessel(vessel, true);
                      }}
                      onKeyDown={() => {}}
                    >
                      {vessel.name}
                    </div>
                    <div className="table-vessel-right-content">
                      <div className="table-ocimf-wrapper">
                        {ocimfPill(
                          vessel.ocimf as VesselCompliance,
                          vessel,
                          visitCrewList,
                          ga4EventTrigger,
                        )}
                      </div>
                      <div className="table-more-options-wrapper">
                        {moreOptions(
                          vessel,
                          visitVessel,
                          visitCrewList,
                          visitEuEts,
                          setActivePopoverVessel,
                          ga4EventTrigger,
                          true,
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </>
          )}
          {!!compliantVessels?.length && (
            <>
              <div className="vessel-table-subheader">
                <strong>{compliantVessels.length}</strong> Compliant Vessels
              </div>
              {[...compliantVessels]
                ?.sort((a, b) => a.name.localeCompare(b.name))
                ?.map((vessel: VesselOwned) => (
                  <div key={vessel.id} className="table-vessel-row">
                    <div
                      className="table-vessel-name"
                      onClick={() => {
                        if (ga4EventTrigger)
                          ga4EventTrigger(
                            'Vessels Widget - Vessel Link',
                            vessel.name,
                            'Link to Vessel Details',
                          );
                        visitVessel(vessel, true);
                      }}
                      onKeyDown={() => {}}
                    >
                      {vessel.name}
                    </div>
                    <div className="table-more-options-wrapper">
                      {moreOptions(
                        vessel,
                        visitVessel,
                        visitCrewList,
                        visitEuEts,
                        setActivePopoverVessel,
                        ga4EventTrigger,
                        false,
                      )}
                    </div>
                  </div>
                ))}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default VesselTable;
