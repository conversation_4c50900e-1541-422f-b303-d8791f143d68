import React, { useState, useRef, useEffect, useContext } from 'react';
import { Col, Container, Row, Card, Modal, Form, Button, Alert } from 'react-bootstrap';
// @ts-ignore
import ReactHtmlParser from 'react-html-parser';
import { CallBackProps } from 'react-joyride';
import VesselList from './VesselList';
import { CustomOverlay, GuardWrapper } from '../common/index';
import TableauWidgets from './TableauWidgets';
import { KeycloakProps } from '../../types/keycloak';
import '../../pages/styles/dashboard.scss';
import { updateUserAttribute } from '../../services/user-service';
import * as spService from '../../services/sharepoint-service';
import NoAccess from './NoAccess';
import { hasAccessToVessel, hasKpiScorecardView, hasNovaView } from '../../utils/roles';
import { GlobalContext } from 'src/context/dashboard-context';
import CrewInjuryStatistics from './CrewInjuryStatistics';
import { WorldMap } from 'src/pages/WorldMap';
import VesselModuleContainer from './VesselWidget/VesselModuleContainer';
import { badgeColors2, tableHeaders3 } from 'src/services/__mocks__/vessel-module-config';
import { getVessels3 } from '../../services/vm-widget-service';
import CardModuleContainer from './iCard/CardModuleContainer';
import { StatCard } from '@paris2/styleguide';
import { WidgetConstant } from './iCard/widget.constant';

const { PARIS_TWO_HOST } = process.env;

function DefaultDashboard({ keycloak }: KeycloakProps) {
  const { ship_party_id, ship_party_type } = keycloak.tokenParsed;
  const { ga4EventTrigger } = useContext(GlobalContext);
  const [run, setRun] = useState(!keycloak.tokenParsed.is_user_onboarded);
  const crewInjuryStatRef = useRef<{ crewStatistics: any }>(null);
  const fleetNewsRef = useRef(null);
  const vesselListRef = useRef(null);
  const fleetStoriesRef = useRef(null);
  const resourceRef = useRef(null);
  const [tcData, setTCData] = useState<any>();
  const [checkTC, setCheckTC] = useState<boolean>(false);
  const [isTcLoading, setTcLoading] = useState<boolean>(true);
  const [modalShow, setModalShow] = useState<boolean>(false);

  // State to hold StatCard data
  const [statCardData, setStatCardData] = useState({
    surveys: { count: 0, isLoading: true },
    deficiencies: { count: 0, isLoading: true },
    riskAssessments: { count: 0, isLoading: true },
    nonCompliantOcimfTankers: { count: 0, isLoading: true },
  });

  const sidebarMenuIcon = document.getElementById('sidebar-menu-icon');

  const hasNovaAccess = hasNovaView(keycloak);

  const isTcDone = !modalShow && !isTcLoading;

  const isTutorialReadyToStartWithNova = run && sidebarMenuIcon && isTcDone;

  const isTutorialReadyToStart = hasNovaAccess ? isTutorialReadyToStartWithNova : run && isTcDone;

  useEffect(() => {
    if (!keycloak.tokenParsed.is_user_onboarded) {
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 1000);
    }

    getTermAndCondition();
  }, []);

  useEffect(() => {
    if (!keycloak.tokenParsed.ship_party_id) {
      return;
    }
    if (
      tcData &&
      (!keycloak.tokenParsed.tc_version ||
        keycloak.tokenParsed.tc_version != parseInt(tcData.OData__UIVersionString, 10))
    ) {
      setModalShow(true);
    } else {
      setModalShow(false);
    }
  }, [keycloak.tokenParsed.tc_version, tcData, keycloak.tokenParsed.ship_party_id]);

  const updateTCVersionAttribute = (conditionVersion) => {
    try {
      setModalShow(false);
      (async function () {
        await updateUserAttribute(keycloak.tokenParsed.preferred_username, {
          attributes: {
            tc_version: parseInt(conditionVersion, 10),
          },
        });
        if (keycloak.tokenParsed.is_user_onboarded) window.location.reload(false);
      })();
    } catch (error) {
      console.log(error);
    }
  };

  const handleFaqLink = () => {
    if (ga4EventTrigger) ga4EventTrigger('Resources', 'Nova Faq');
    window.open(`${PARIS_TWO_HOST}/nova/faq`);
  };

  const getTermAndCondition = async () => {
    try {
      const value = await spService.getTermCondition();
      setTCData(value.data.data);
      setTcLoading(false);
    } catch (error) {
      console.log(error);
    }
  };

  const checkedItem = (event) => {
    setCheckTC(event.target.checked);
  };

  const updateIsUserOnboardedAttribute = async () => {
    try {
      await updateUserAttribute(keycloak.tokenParsed.preferred_username, {
        attributes: {
          is_user_onboarded: true,
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  // const handleClickSidebarAutomatically = () => {
  //   const backdrop = document.getElementsByClassName('sideBar__modal') as HTMLCollectionOf<HTMLDivElement>;
  //   backdrop[0]?.click();
  // };

  const handleOverlayCallback = async (props: CallBackProps) => {
    const { status } = props;
    if (status === 'finished' || status === 'skipped') {
      console.log('onboarding finished');
      setRun(false);
      window.scrollTo(0, 0);
      await updateIsUserOnboardedAttribute();
    }
  };

  const getOverlays = () => {
    const crewStatisticRef = crewInjuryStatRef.current?.crewStatistics;
    const getOverlaySteps = [
      {
        target: '#main-home-dashboard-container',
        content: "Welcome to the New PARIS 2.0, see what's new!",
        disableBeacon: true,
        spotlightPadding: 0,
        placement: 'center' as const,
      },
      {
        target: '#crew-injury-stats-block',
        content:
          "Here's a snapshot of the crew injury statistics across all of FLEET's 600+ vessels",
        disableBeacon: true,
        spotlightPadding: 0,
        placement: 'bottom' as const,
      },
      {
        target: '#nova-demo-youtube-block',
        content: 'Watch a NOVA demo',
        disableBeacon: true,
        spotlightPadding: 0,
        placement: 'top' as const,
      },
    ];
    // sidebarMenuIcon
    const overlayStepsWithNova = [
      ...getOverlaySteps,
      {
        content: 'Introducing NOVA - our data analytics platform ',
        disableBeacon: true,
        spotlightPadding: 0,
        placement: 'top' as const,
        target: '#welcome-nova-banner-block',
        offset: -5,
        styles: {
          left: '0%',
        },
      },
    ];

    const overlaySteps = hasNovaView(keycloak) ? overlayStepsWithNova : getOverlaySteps;

    return (
      <>
        {crewStatisticRef && (
          <CustomOverlay
            run={run}
            steps={overlaySteps}
            floaterProps={{ hideArrow: true }}
            continuous
            handleOverlayCallback={handleOverlayCallback}
            styles={{
              options: {
                zIndex: 10000,
              },
            }}
          />
        )}
        {/* <Backdrop show>
          <div />
        </Backdrop> */}
      </>
    );
  };

  const visitNova = () => {
    if (ga4EventTrigger) ga4EventTrigger('NOVA Link Banner', 'NOVA Link Banner');
  };

  const youtubeClick = () => {
    if (ga4EventTrigger) ga4EventTrigger('Youtube Tutorial', 'Youtube Tutorial');
  };

  const opts = {
    height: '248',
    width: '531',
  };

  const novaLink = () =>
    hasKpiScorecardView(keycloak) ? (
      <>
        the{' '}
        <a href="/dashboard/kpi-scorecard" onClick={visitNova}>
          {' '}
          KPI Scorecard Analytics page.
        </a>
      </>
    ) : (
      <>
        our{' '}
        <a href="/nova" onClick={visitNova}>
          {' '}
          Data Analytics Platform NOVA.
        </a>
      </>
    );

  // Handler to update StatCard data from CardModuleContainer
  const handleStatCardDataUpdate = (
    cardType: string,
    data: { count: number; isLoading: boolean },
  ) => {
    setStatCardData((prev) => ({
      ...prev,
      [cardType]: data,
    }));
  };

  const isCurrentUserVm = keycloak?.tokenParsed?.email === '<EMAIL>';
  return (
    <>
      {isCurrentUserVm ? (
        <>
          <Container
            fluid
            className="vm-landing-page-container"
            id="main-home-vm-dashboard-container"
          >
            <div className="stat-card-wrapper">
              <StatCard
                title="Surveys and Certificate"
                count={statCardData.surveys.count}
                subTitle="Expiring within 30 days"
                isLoading={statCardData.surveys.isLoading}
              />
              <StatCard
                title="Deficiencies"
                count={statCardData.deficiencies.count}
                subTitle="Overdue"
                isLoading={statCardData.deficiencies.isLoading}
                onClick={() => {}}
              />
              <StatCard
                title="Unassigned Risk Assessment"
                count={statCardData.riskAssessments.count}
                isLoading={statCardData.riskAssessments.isLoading}
                onClick={() => {}}
              />
              <StatCard
                title="Non-Compliant OCIMF Tankers"
                count={68}
                isLoading={statCardData.nonCompliantOcimfTankers.isLoading}
              />
            </div>
            {/* {isTutorialReadyToStart && getOverlays()} */}
            <Row
              className="justify-content-md-center tableau-widget widget-padding-top"
              id="vm-tableau-widget"
              style={{ backgroundColor: 'transparent' }}
            >
              <Col xs={12} md={6} className="py-3">
                <div className="">
                  <WorldMap
                    containerClassName="pl-0 pr-0"
                    mapContainerStyle={{ height: '490px' }}
                    keycloak={keycloak}
                  />
                </div>
              </Col>
              <Col xs={12} md={6} className="py-3">
                <CardModuleContainer configKey={WidgetConstant.ITINERARY_ETA} />
              </Col>
            </Row>
            <Row className="justify-content-md-center tableau-widget transparent-no-pt">
              <Col xs={12} md={6} className="py-3">
                <CardModuleContainer
                  configKey={WidgetConstant.SURVEYS_CERTIFICATES}
                  onStatCardDataUpdate={(data) => handleStatCardDataUpdate('surveys', data)}
                />
              </Col>
              <Col xs={12} md={6} className="py-3">
                <CardModuleContainer
                  configKey={WidgetConstant.DEFICIENCIES}
                  onStatCardDataUpdate={(data) => handleStatCardDataUpdate('deficiencies', data)}
                />
              </Col>
            </Row>
            <Row className="justify-content-md-center tableau-widget transparent-no-pt">
              <Col xs={12} md={6} className="py-3">
                <CardModuleContainer
                  configKey={WidgetConstant.RISK_ASSESSMENT}
                  onStatCardDataUpdate={(data) => handleStatCardDataUpdate('riskAssessments', data)}
                />
              </Col>
              <Col xs={12} md={6} className="py-3">
                <CardModuleContainer configKey={WidgetConstant.OWNER_FINANCIAL_REPORTING} />
              </Col>
            </Row>

            <Row className="justify-content-md-center tableau-widget transparent-no-pt">
              <Col xs={12} md={6} className="py-3">
                <div className="h-100 widget-border-vm" ref={vesselListRef}>
                  <GuardWrapper
                    hasAccess={hasAccessToVessel(keycloak)}
                    fallback={
                      <Card body>
                        <p className="report-title" ref={fleetStoriesRef}>
                          Vessels
                        </p>
                        <NoAccess />
                      </Card>
                    }
                  >
                    <VesselList
                      shipPartyId={ship_party_id}
                      onStatCardDataUpdate={(data) =>
                        handleStatCardDataUpdate('nonCompliantOcimfTankers', data)
                      }
                    />
                  </GuardWrapper>
                </div>
              </Col>
              <Col xs={12} md={6} className="py-3"></Col>
            </Row>
          </Container>
        </>
      ) : (
        <>
          <Modal
            show={modalShow}
            data-testid="term-condition-area"
            onHide={() => setModalShow(false)}
            aria-labelledby="confirmation-modal"
            dialogClassName="terms-condition-modal"
            centered
            backdrop="static"
            keyboard={false}
            scrollable
          >
            <Modal.Header>
              <Modal.Title id="confirmation-modal" style={{ borderBottom: '0' }}>
                PARIS 2.0 - Terms and Conditions
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>{tcData && ReactHtmlParser(tcData.CanvasContent1)}</Modal.Body>
            <Modal.Footer style={{ borderTop: '0' }}>
              <Form.Check style={{ cursor: 'pointer' }}>
                <Form.Check.Input
                  id="terms-condition-checkbox"
                  type="checkbox"
                  checked={checkTC}
                  onChange={checkedItem}
                />
                <Form.Check.Label htmlFor="terms-condition-checkbox">
                  Accept the conditions
                </Form.Check.Label>
              </Form.Check>
              {/* <Form.Check
            className="check-condition"
            type="checkbox"
            label="Accept the conditions"
            checked={checkTC}
            onChange={checkedItem}
          /> */}
              <Button
                variant="secondary"
                disabled={!checkTC}
                onClick={() => {
                  updateTCVersionAttribute(tcData.OData__UIVersionString);
                }}
              >
                Confirm
              </Button>
            </Modal.Footer>
          </Modal>
          <Container fluid className="landing-page-container" id="main-home-dashboard-container">
            {isTutorialReadyToStart && getOverlays()}
            <Row
              className="justify-content-md-center tableau-widget widget-padding-top"
              id="tableau-widget"
              style={{ backgroundColor: 'transparent' }}
            >
              <div className="justify-content-md-center banner">
                {hasNovaView(keycloak) && (
                  <Alert variant="info" id="welcome-nova-banner-block">
                    <span className="welcome-text-nova">
                      Welcome to the new PARIS 2.0! Make sure you check out {novaLink()}
                    </span>
                  </Alert>
                )}
              </div>
              <div className="max-width-wrapper">
                <Col xs={12} md={4} className="py-3">
                  <TableauWidgets keycloak={keycloak} />
                </Col>
                <Col xs={12} md={8} className="py-3">
                  <div className="report-container widget-border card">
                    <WorldMap
                      containerClassName="pl-0 pr-0"
                      mapContainerStyle={{ height: '414px' }}
                      keycloak={keycloak}
                    />
                  </div>
                </Col>
              </div>
            </Row>
            <Row className="justify-content-md-center tableau-widget transparent-no-pt">
              <div className="max-width-wrapper">
                <Col xs={12} md={6} className="py-3">
                  <div className="h-100" ref={vesselListRef}>
                    <GuardWrapper
                      hasAccess={hasAccessToVessel(keycloak)}
                      fallback={
                        <Card body className="report-container widget-border">
                          <p className="report-title" ref={fleetStoriesRef}>
                            Vessels
                          </p>
                          <NoAccess />
                        </Card>
                      }
                    >
                      <VesselList shipPartyId={ship_party_id} shipPartyType={ship_party_type} />
                    </GuardWrapper>
                  </div>
                </Col>
                <Col xs={12} md={6} className="py-3">
                  <CrewInjuryStatistics ref={crewInjuryStatRef} />
                </Col>
                <Col xs={12} md={6} className="py-3">
                  <VesselModuleContainer
                    title="Risk Assessment"
                    fetchFn={getVessels3}
                    tabs={[]} // An empty array correctly signals that only the "All" view should be shown
                    tableHeaders={tableHeaders3}
                    badgeColors={badgeColors2}
                    sizeKey="md"
                    IsiconRenderVisible={false}
                    IsenLargeIconVisible={true}
                    IsVesselSelectVisible={true}
                    vesselSelectPosition="after"
                    gridComponent={'dashboard'}
                    defaultComponent={'list'}
                    cellStyleType="conditional"
                  />
                </Col>
              </div>
            </Row>
          </Container>
        </>
      )}
    </>
  );
}

export { DefaultDashboard };
