/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/order */
/* eslint-disable object-curly-newline */
/* eslint-disable max-len */
import React, { useRef, useEffect, useState, useContext } from 'react';
import { Col, Card, Row, Modal, Button, Form } from 'react-bootstrap';
import { GlobalContext } from '../../context/dashboard-context';
import { KeycloakProps } from 'src/types/keycloak';
import moment from 'moment-timezone';
import { AnalogClock, TimezoneDropdown } from '../common/index';
import { updateUserAttribute } from '../../services/keycloack-service';
import { TimezoneDropdownType } from '../../types/widgets';
import { DEFAULT_TIMEZONES, OTHER_TIMEZONES } from '../../constants/widgets';
import _ from 'lodash';

const DEFAULT_NUMBER_OF_DASHBOARDS = 2;

const ALL_TIMEZONES: TimezoneDropdownType[] = [
  ...moment.tz.names().map((ele: string) => {
    const cityArr = ele.split('/');
    const city = cityArr[cityArr.length - 1].split('_').join(' ');
    return {
      city,
      timezone: ele,
    };
  }),
  ...OTHER_TIMEZONES,
];

const TableauWidgets = ({ keycloak }: KeycloakProps) => {
  const { ga4EventTrigger, ga4PageView } = useContext(GlobalContext);
  const [modalShow, setModalShow] = useState(false);
  const [userZoneInfoList, setUserZoneInfoList] = useState<TimezoneDropdownType[]>(
    DEFAULT_TIMEZONES,
  );
  const selectedTimezone = useRef({
    ind: 0,
    city: '',
    timezone: '',
  });

  const updatezoneinfoAttribute = async (zoneinfo: string) => {
    try {
      await updateUserAttribute(keycloak.tokenParsed.preferred_username, {
        attributes: {
          zoneinfo,
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (ga4PageView) ga4PageView('/home', '', 'PARIS 2.0');
  }, [ga4PageView]);

  useEffect(() => {
    if (keycloak.tokenParsed.zoneinfo) {
      const zoneinfo = keycloak.tokenParsed.zoneinfo.split(',');
      const zones = ALL_TIMEZONES.filter(ele => zoneinfo.includes(ele.city));
      setUserZoneInfoList(zones);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const labelClickedHandler = (ind: number) => {
    if (ga4EventTrigger) ga4EventTrigger('World Clock', _.toString(userZoneInfoList[ind].city));
    setModalShow(true);
    selectedTimezone.current.ind = ind;
  };

  const clock = () => (
    <>
      <Row className="ml-auto mr-auto">
        {userZoneInfoList.map((ele, ind) => {
          const classes = ind === 6 || ind === 7 ? 'd-none d-xl-block' : '';
          return (
            <Col key={ele.city} xs={6} sm={3} md={6} lg={4} xl={3} className={classes}>
              <AnalogClock
                city={ele.city}
                timezone={ele.timezone}
                labelClicked={labelClickedHandler}
                ind={ind}
              />
            </Col>
          );
        })}
      </Row>
    </>
  );

  const countryChangeHandler = (selectedTimezoneValue: TimezoneDropdownType) => {
    selectedTimezone.current.timezone = selectedTimezoneValue.timezone;
    selectedTimezone.current.city = selectedTimezoneValue.city;
  };

  const modalCancelHandler = () => {
    selectedTimezone.current.timezone = '';
    setModalShow(false);
  };

  const modalConfirmHandler = () => {
    setModalShow(false);
    if (selectedTimezone.current.timezone) {
      const newArr = userZoneInfoList.map((ele, index) => {
        if (index === selectedTimezone.current.ind) {
          return {
            city: selectedTimezone.current.city,
            timezone: selectedTimezone.current.timezone,
          };
        }
        return ele;
      });
      setUserZoneInfoList(newArr);
      updatezoneinfoAttribute(newArr.map(ele => ele.city).join(','));
    }
  };

  return (
    <>
      <Modal
        data-testid="term-condition-area"
        show={modalShow}
        onHide={() => setModalShow(false)}
        aria-labelledby="confirmation-modal"
        centered
        backdrop="static"
        keyboard={false}
      >
        <Modal.Header>
          <Modal.Title id="confirmation-modal" style={{ borderBottom: '0' }}>
            Select City
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <TimezoneDropdown
              label="City"
              optionsList={ALL_TIMEZONES}
              onChange={countryChangeHandler}
            />
          </Form>
        </Modal.Body>
        <Modal.Footer style={{ borderTop: '0' }}>
          <Button variant="primary" onClick={modalCancelHandler}>
            Cancel
          </Button>
          <Button variant="secondary" onClick={modalConfirmHandler}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
      <div className="report-container widget-border card">
        <Card.Body className="">
          <p className="report-title">World Clock</p>
          {clock()}
        </Card.Body>
      </div>
    </>
  );
};

export default TableauWidgets;
