import React, { useContext, useEffect, useState } from 'react';
import { Col, Row, Card, Form, Spinner, InputGroup } from 'react-bootstrap';
import { Icon } from '../../StyleGuide';
import VesselTable from './VesselTable';
import { VesselOwned } from '../../types/vessel';
import { GlobalContext } from '../../context/dashboard-context';
import { VesselCompliance, getPrecomputedCompliances } from '../../services/seafarer-service';
import moment from 'moment';
import { VESSEL_TYPES } from '../../constants/vessel';
import { getOwnerships } from 'src/services/vessel-service';

const { PARIS_ONE_HOST, BASE_URL } = process.env;

const VesselList = ({
  shipPartyId,
  onStatCardDataUpdate,
}: {
  shipPartyId: number | null | undefined;
  onStatCardDataUpdate?: (data: { count: number; isLoading: boolean }) => void;
}) => {
  const [filteredVesselList, setFilteredVesselList] = useState<VesselOwned[]>([]);
  const [filteredVesselListNonCompliant, setFilteredVesselListNonCompliant] = useState<
    (VesselOwned & { ocimf?: VesselCompliance })[]
  >([]);
  const [isPreComputeApiCalled, setIsPreComputeApiCalled] = useState<boolean>(false);
  const [searchedVesselName, setSearchedVesselName] = useState<string | null>(null);
  const [ocimfComplianceData, setOcimfComplianceData] = useState<any>([]);
  const { ga4EventTrigger } = useContext(GlobalContext);
  const [isVesselLoading, setIsVesselLoading] = useState(false);
  const [vesselList, setVesselList] = useState([]);
  const getMinimalVesselList = async () => {
    try {
      setIsVesselLoading(true);
      const minimalVesselList = await getOwnerships('f=name&f=id&f=vessel.id&f=vessel_type.type');
      const preparedVesselList = minimalVesselList?.data?.results?.map(vessel => ({
        id: vessel.id,
        name: vessel?.name,
        vessel_id: vessel?.vessel?.id,
        vessel_type: vessel?.vessel_type?.type,
      }));
      setVesselList(preparedVesselList);
      setIsVesselLoading(false);
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    getMinimalVesselList();
  }, []);

  const handleVesselNameChange = value => {
    if (ga4EventTrigger)
      ga4EventTrigger('Vessels Widget - Vessel Search', value, 'Filter of Vessel');
    setSearchedVesselName(value.toLowerCase());
  };

  const prepareAndSetComplianceData = (complianceData: VesselCompliance[], returnData = false) => {
    const preparedCompliantVessels: VesselOwned[] = [];
    const preparedNonCompliantVessels: (VesselOwned & { ocimf?: VesselCompliance })[] = [];
    const nonCompliantVesselIds = complianceData?.map(vessel => vessel.vessel_id);
    vesselList.forEach(vessel => {
      if (nonCompliantVesselIds.includes(vessel.vessel_id)) {
        const ocimfData = complianceData?.find(ocimf => ocimf.vessel_id === vessel.vessel_id);
        preparedNonCompliantVessels.push({ ...vessel, ocimf: ocimfData });
      } else {
        preparedCompliantVessels.push(vessel);
      }
    });
    if (returnData) return { preparedCompliantVessels, preparedNonCompliantVessels };
    setFilteredVesselList(preparedCompliantVessels);
    setFilteredVesselListNonCompliant(preparedNonCompliantVessels);
  };

  const getOcimfComplianceList = async () => {
    const payload = {
      vessel_ids: vesselList
        ?.filter(vessel => vessel?.vessel_type === VESSEL_TYPES.TANKER)
        .map(vessel => vessel.vessel_id),
    };
    const ocimfComplianceData = await getPrecomputedCompliances(payload);
    prepareAndSetComplianceData(ocimfComplianceData.data);
    setOcimfComplianceData(ocimfComplianceData);
    setIsPreComputeApiCalled(true);
  };

  useEffect(() => {
    if (vesselList?.length) {
      getOcimfComplianceList();
    }
  }, [vesselList]);

  useEffect(() => {
    if (!isPreComputeApiCalled) return;
    const { preparedCompliantVessels, preparedNonCompliantVessels } = prepareAndSetComplianceData(
      ocimfComplianceData?.data,
      true,
    ) as {
      preparedCompliantVessels: VesselOwned[];
      preparedNonCompliantVessels: (VesselOwned & { ocimf?: VesselCompliance })[];
    };
    if (searchedVesselName) {
      setFilteredVesselList(
        preparedCompliantVessels.filter((vessel: VesselOwned) =>
          vessel?.name?.toLowerCase()?.includes(searchedVesselName),
        ),
      );
      setFilteredVesselListNonCompliant(
        preparedNonCompliantVessels.filter((vessel: VesselOwned & { ocimf?: VesselCompliance }) =>
          vessel?.name?.toLowerCase()?.includes(searchedVesselName),
        ),
      );
    } else {
      setFilteredVesselList(preparedCompliantVessels);
      setFilteredVesselListNonCompliant(preparedNonCompliantVessels);
      onStatCardDataUpdate?.({
        count: preparedNonCompliantVessels.length,
        isLoading: false,
      });
    }
  }, [searchedVesselName, isPreComputeApiCalled]);

  const visitVessel = (vessel: VesselOwned, skipGa = false) => {
    if (ga4EventTrigger && !skipGa)
      ga4EventTrigger('Vessels Widget - More Menu', 'Vessel Details', 'Select Link');
    if (shipPartyId) {
      const link = '/FMLLoginKeycloak?targeturl=/fml';
      window.open(
        `${PARIS_ONE_HOST}/fml${link}/PARIS?display=ship&shipid=${vessel.ref_id}`,
        '_blank',
      );
    } else window.open(`${BASE_URL}/vessel/ownership/details/${vessel.id}`, '_blank');
  };

  const visitCrewList = (
    vessel: VesselOwned,
    shouldVisitCrewListWithOcimf = false,
    skipGa = false,
  ) => {
    if (ga4EventTrigger && !skipGa)
      ga4EventTrigger('Vessels Widget - More Menu', 'Crew List', 'Select Link');
    window.open(
      `${BASE_URL}/seafarer/crew-list/vessel/${vessel.vessel_id}${
        shouldVisitCrewListWithOcimf
          ? '/' + moment().format('YYYY-MM-DD') + '?is_ocimf_enabled=true'
          : ''
      }`,
      '_blank',
    );
  };

  const visitEuEts = (vessel: VesselOwned) => {
    if (ga4EventTrigger)
      ga4EventTrigger('Vessels Widget - More Menu', 'EU-ETS Report', 'Select Link');
    window.open(
      `${BASE_URL}/vessel/report/environmental/ets?&vessel_ownership_id=${vessel.id}`,
      '_blank',
    );
  };

  return (
    <div className="h-100">
      <Card body className="report-container widget-border">
        <div className="vessel-list-title-wrapper">
          <div className="report-title-wrapper">
            <div className="report-title align-left">Vessels</div>
            {
              <div className="last-updated-at">
                Last Updated on:{' '}
                {isPreComputeApiCalled && filteredVesselListNonCompliant?.length
                  ? moment(
                      filteredVesselListNonCompliant.reduce((latest: Date | null, vessel) => {
                        const dateStr = vessel?.ocimf?.final_updated_at;
                        if (!dateStr) return latest;
                        const currentDate = new Date(dateStr);
                        return !latest || currentDate > latest ? currentDate : latest;
                      }, null),
                    )?.format('DD MMM YYYY') ?? '---'
                  : '---'}
              </div>
            }
          </div>
          <Form.Group as={Col} className="search-text-with-icon-wrapper">
            <InputGroup>
              <InputGroup.Text className="search-icon-wrapper">
                <Icon icon="search" />
              </InputGroup.Text>
              <Form.Control
                data-testid="search-vessel-dropdown"
                type="text"
                name="vessel_name"
                placeholder="Filter by Vessel Name"
                className="vessel-search-input"
                value={searchedVesselName}
                onChange={e => handleVesselNameChange(e.target.value)}
              />
            </InputGroup>
            <InputGroup.Text
              className="clear-icon-wrapper"
              onClick={() => setSearchedVesselName('')}
              data-testid="clear-icon-id"
            >
              {!!searchedVesselName?.length && <Icon icon="close" />}
            </InputGroup.Text>
          </Form.Group>
        </div>
        <Row>
          {isVesselLoading || !isPreComputeApiCalled ? (
            <div className="vessel-loading-wrapper" data-testid="vessel-loading-wrapper">
              <Spinner animation="border" />
            </div>
          ) : (
            <VesselTable
              compliantVessels={filteredVesselList}
              nonCompliantVessels={filteredVesselListNonCompliant}
              isPreComputeApiCalled={isPreComputeApiCalled}
              visitVessel={visitVessel}
              visitCrewList={visitCrewList}
              visitEuEts={visitEuEts}
              ga4EventTrigger={ga4EventTrigger}
            />
          )}
        </Row>
      </Card>
    </div>
  );
};

export default VesselList;
