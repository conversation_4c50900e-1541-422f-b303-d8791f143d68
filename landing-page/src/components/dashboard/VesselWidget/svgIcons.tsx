import React from 'react';

export const EnlargeIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props} // Spread all passed props like className, onClick, aria-label, etc.
    >
      <path
        d="M17.6929 3.94936V8.23187H19V1.7327H12.5008V3.03977H16.7833L2.30707 17.516V13.2335H1V19.7327H7.49918V18.4256H3.21667L17.6929 3.94936Z"
        fill="#1F4A70"
      />
    </svg>
  );
};

export const MinimizeIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.2044 7.88439V2.8426H14.875V10.1544H22.1868V8.82495H17.145L23.6524 2.31416L22.7152 1.37692L16.2044 7.88439Z"
        fill="#1F4A70"
      />
      <path
        d="M9.5574 16.4125V21.4543H10.8868V14.1426H3.57505V15.472H8.61684L2.10938 21.9828L3.04661 22.92L9.5574 16.4125Z"
        fill="#1F4A70"
      />
    </svg>
  );
};

export const ExternalLinkIcon = (props: React.SVGAttributes<SVGElement>) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props} // Spread all props for flexibility
  >
    <path
      d="M15.3337 17.7987H4.66699C4.13656 17.7987 3.62785 17.588 3.25278 17.213C2.87771 16.8379 2.66699 16.3292 2.66699 15.7987V5.13208C2.66699 4.60165 2.87771 4.09294 3.25278 3.71787C3.62785 3.34279 4.13656 3.13208 4.66699 3.13208H8.66699C8.8438 3.13208 9.01337 3.20232 9.1384 3.32734C9.26342 3.45237 9.33366 3.62194 9.33366 3.79875C9.33366 3.97556 9.26342 4.14513 9.1384 4.27015C9.01337 4.39518 8.8438 4.46541 8.66699 4.46541H4.66699C4.49018 4.46541 4.32061 4.53565 4.19559 4.66068C4.07056 4.7857 4.00033 4.95527 4.00033 5.13208V15.7987C4.00033 15.9756 4.07056 16.1451 4.19559 16.2702C4.32061 16.3952 4.49018 16.4654 4.66699 16.4654H15.3337C15.5105 16.4654 15.68 16.3952 15.8051 16.2702C15.9301 16.1451 16.0003 15.9756 16.0003 15.7987V11.7987C16.0003 11.6219 16.0706 11.4524 16.1956 11.3273C16.3206 11.2023 16.4902 11.1321 16.667 11.1321C16.8438 11.1321 17.0134 11.2023 17.1384 11.3273C17.2634 11.4524 17.3337 11.6219 17.3337 11.7987V15.7987C17.3337 16.3292 17.1229 16.8379 16.7479 17.213C16.3728 17.588 15.8641 17.7987 15.3337 17.7987ZM8.66699 12.4654C8.57925 12.4659 8.49228 12.4491 8.41105 12.4159C8.32983 12.3828 8.25595 12.3339 8.19366 12.2721C8.13117 12.2101 8.08158 12.1364 8.04773 12.0551C8.01389 11.9739 7.99646 11.8868 7.99646 11.7987C7.99646 11.7107 8.01389 11.6236 8.04773 11.5424C8.08158 11.4611 8.13117 11.3874 8.19366 11.3254L15.0603 4.46541H12.667C12.4902 4.46541 12.3206 4.39518 12.1956 4.27015C12.0706 4.14513 12.0003 3.97556 12.0003 3.79875C12.0003 3.62194 12.0706 3.45237 12.1956 3.32734C12.3206 3.20232 12.4902 3.13208 12.667 3.13208H16.667C16.8438 3.13208 17.0134 3.20232 17.1384 3.32734C17.2634 3.45237 17.3337 3.62194 17.3337 3.79875V7.79875C17.3337 7.97556 17.2634 8.14513 17.1384 8.27015C17.0134 8.39518 16.8438 8.46541 16.667 8.46541C16.4902 8.46541 16.3206 8.39518 16.1956 8.27015C16.0706 8.14513 16.0003 7.97556 16.0003 7.79875V5.40541L9.14033 12.2721C9.07803 12.3339 9.00415 12.3828 8.92293 12.4159C8.8417 12.4491 8.75473 12.4659 8.66699 12.4654Z"
      fill="#1F4A70"
    />
  </svg>
);

export const RaCheckedIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="19"
      viewBox="0 0 20 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect x="1" y="0.499939" width="18" height="18" rx="2" fill="#0091B8" stroke="#0091B8" />
      <path
        d="M14.5363 6.20876C14.2578 5.93033 13.8402 5.93033 13.5618 6.20876L8.34118 11.4294L6.18333 9.27151C5.9049 8.99308 5.48725 8.99308 5.20882 9.27151C4.93039 9.54994 4.93039 9.96759 5.20882 10.246L7.85392 12.8911C7.99314 13.0303 8.13235 13.0999 8.34118 13.0999C8.55 13.0999 8.68921 13.0303 8.82843 12.8911L14.5363 7.18327C14.8147 6.90484 14.8147 6.48719 14.5363 6.20876Z"
        fill="white"
      />
    </svg>
  );
};

export const RaUncheckedIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="1" y="1" width="18" height="18" rx="2" fill="white" stroke="#CCCCCC" />
    </svg>
  );
};

export const CommentIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_2624_4272)">
        <path
          d="M17.3335 4.875C17.3335 4.37772 17.136 3.90081 16.7843 3.54917C16.4327 3.19754 15.9558 3 15.4585 3H4.2085C3.71122 3 3.2343 3.19754 2.88267 3.54917C2.53104 3.90081 2.3335 4.37772 2.3335 4.875L2.3335 12.375C2.3335 12.8723 2.53104 13.3492 2.88267 13.7008C3.2343 14.0525 3.71122 14.25 4.2085 14.25H13.1954C13.444 14.2501 13.6824 14.3489 13.8582 14.5247L16.5329 17.1994C16.5984 17.265 16.6819 17.3098 16.7728 17.328C16.8638 17.3462 16.9581 17.337 17.0438 17.3015C17.1295 17.2661 17.2028 17.206 17.2544 17.1289C17.3059 17.0519 17.3335 16.9612 17.3335 16.8684V4.875ZM5.61475 5.8125H14.0522C14.1766 5.8125 14.2958 5.86189 14.3837 5.94979C14.4716 6.0377 14.521 6.15693 14.521 6.28125C14.521 6.40557 14.4716 6.5248 14.3837 6.61271C14.2958 6.70061 14.1766 6.75 14.0522 6.75H5.61475C5.49043 6.75 5.3712 6.70061 5.28329 6.61271C5.19538 6.5248 5.146 6.40557 5.146 6.28125C5.146 6.15693 5.19538 6.0377 5.28329 5.94979C5.3712 5.86189 5.49043 5.8125 5.61475 5.8125ZM5.61475 8.15625H14.0522C14.1766 8.15625 14.2958 8.20564 14.3837 8.29354C14.4716 8.38145 14.521 8.50068 14.521 8.625C14.521 8.74932 14.4716 8.86855 14.3837 8.95646C14.2958 9.04436 14.1766 9.09375 14.0522 9.09375H5.61475C5.49043 9.09375 5.3712 9.04436 5.28329 8.95646C5.19538 8.86855 5.146 8.74932 5.146 8.625C5.146 8.50068 5.19538 8.38145 5.28329 8.29354C5.3712 8.20564 5.49043 8.15625 5.61475 8.15625ZM5.61475 10.5H10.3022C10.4266 10.5 10.5458 10.5494 10.6337 10.6373C10.7216 10.7252 10.771 10.8444 10.771 10.9688C10.771 11.0931 10.7216 11.2123 10.6337 11.3002C10.5458 11.3881 10.4266 11.4375 10.3022 11.4375H5.61475C5.49043 11.4375 5.3712 11.3881 5.28329 11.3002C5.19538 11.2123 5.146 11.0931 5.146 10.9688C5.146 10.8444 5.19538 10.7252 5.28329 10.6373C5.3712 10.5494 5.49043 10.5 5.61475 10.5Z"
          fill="#1F4A70"
        />
      </g>
      <defs>
        <clipPath id="clip0_2624_4272">
          <rect width="15" height="15" fill="white" transform="translate(2.3335 3)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const ThreeDotsMenuIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M4.2665 11.5999C5.15016 11.5999 5.8665 10.8836 5.8665 9.9999C5.8665 9.11625 5.15016 8.3999 4.2665 8.3999C3.38285 8.3999 2.6665 9.11625 2.6665 9.9999C2.6665 10.8836 3.38285 11.5999 4.2665 11.5999Z"
        fill="#1C1F4A"
      />
      <path
        d="M10.6665 11.5999C11.5502 11.5999 12.2665 10.8836 12.2665 9.9999C12.2665 9.11625 11.5502 8.3999 10.6665 8.3999C9.78287 8.3999 9.06653 9.11625 9.06653 9.9999C9.06653 10.8836 9.78287 11.5999 10.6665 11.5999Z"
        fill="#1C1F4A"
      />
      <path
        d="M17.0666 11.5999C17.9502 11.5999 18.6666 10.8836 18.6666 9.9999C18.6666 9.11625 17.9502 8.3999 17.0666 8.3999C16.1829 8.3999 15.4666 9.11625 15.4666 9.9999C15.4666 10.8836 16.1829 11.5999 17.0666 11.5999Z"
        fill="#1C1F4A"
      />
    </svg>
  );
};
