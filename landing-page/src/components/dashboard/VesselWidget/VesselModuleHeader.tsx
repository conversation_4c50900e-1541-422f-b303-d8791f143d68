import React from 'react';
import { LayoutGrid, List } from 'lucide-react';
import classNames from 'classnames';
import { EnlargeIcon, MinimizeIcon } from './svgIcons';
import styles from '../styles/vessel-widget-scss/VesselModule.module.scss'; 

interface VesselModuleHeaderProps {
  title: string;
  viewMode: 'list' | 'grid';
  isModal: boolean;
  IsiconRenderVisible?: boolean;
  IsenLargeIconVisible?: boolean;
  onViewModeChange: (mode: 'list' | 'grid') => void;
  onToggleModal: () => void;
}

export const VesselModuleHeader: React.FC<VesselModuleHeaderProps> = ({
  title,
  viewMode,
  isModal,
  IsiconRenderVisible,
  IsenLargeIconVisible,
  onViewModeChange,
  onToggleModal,
}) => {
  return (
    <div className={styles['ra-vessel-module-header']}>
      <h2 className={styles['ra-vessel-module-title']}>{title}</h2>
      <div className={styles['ra-vessel-module-controls']}>
        {IsiconRenderVisible && (
          <div className={styles['ra-view-toggle-container']}>
            <button
              onClick={() => onViewModeChange('grid')}
              className={classNames(styles['ra-view-toggle-button'], {
                [styles['active']]: viewMode === 'grid',
              })}
              aria-label="Grid view"
            >
              <LayoutGrid className={styles['ra-view-toggle-icon']} />
            </button>
            <button
              onClick={() => onViewModeChange('list')}
              className={classNames(styles['ra-view-toggle-button'], {
                [styles['active']]: viewMode === 'list',
              })}
              aria-label="List view"
            >
              <List className={styles['ra-view-toggle-icon']} />
            </button>
          </div>
        )}

        {IsenLargeIconVisible &&
          (isModal ? (
            <MinimizeIcon
              className={styles['ra-collapse-icon']}
              onClick={onToggleModal}
              aria-label="Minimize view"
            />
          ) : (
            <EnlargeIcon
              className={styles['ra-enlarge-icon']}
              onClick={onToggleModal}
              aria-label="Enlarge view"
            />
          ))}
      </div>
    </div>
  );
};