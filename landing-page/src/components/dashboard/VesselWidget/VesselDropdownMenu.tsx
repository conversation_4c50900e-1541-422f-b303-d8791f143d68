import React from 'react';
import { Search } from 'lucide-react';
import {
  DropdownFooterProps,
  SearchInputProps,
  VesselDropdownMenuProps,
  VesselGroupListProps,
} from '../../../types/types';
import styles from '../styles/vessel-widget-scss/VesselDropdown.module.scss';
import { RaCheckedIcon, RaUncheckedIcon } from './svgIcons';

// --- Search Input Sub-component ---

const SearchInput: React.FC<SearchInputProps> = React.memo(({ searchTerm, onSearchChange }) => (
  <div className={styles.raSearchContainer}>
    <Search size={16} className={styles.raSearchIcon} />
    <input
      type="text"
      placeholder="Search"
      value={searchTerm}
      onChange={(e) => onSearchChange(e.target.value)}
      className={styles.raSearchInput}
      aria-label="Search vessels"
    />
  </div>
));

// --- Vessel List Sub-component ---

const VesselGroupList: React.FC<VesselGroupListProps> = ({
  filteredGroups,
  selectedVessels,
  onToggleVessel,
}) => {
  return (
    <div className={styles.raVesselGroups}>
      {filteredGroups.map((group) => (
        <div key={`group-${group?.id}`} className={styles.raVesselGroup}>
          <ul className={styles.raVesselList}>
            {group.vessels.map((vessel) => (
              <li
                key={`vessel-${group?.id}-${vessel?.vessel_id}-${vessel?.vessel_ownership_id}`}
                className={styles.raVesselItem}
              >
                <label>
                    <div
                    onClick={() => onToggleVessel(vessel.name)}
                    className={styles.raCheckbox}
                    role="checkbox"
                    aria-checked={selectedVessels.includes(vessel.name)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                      onToggleVessel(vessel.name);
                      }
                    }}
                    tabIndex={0}
                    >
                    {selectedVessels.includes(vessel.name) ? (
                      <RaCheckedIcon />
                    ) : (
                      <RaUncheckedIcon />
                    )}
                    </div>
                  {vessel.name}
                </label>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
};
// --- Footer Sub-component ---

const DropdownFooter: React.FC<DropdownFooterProps> = React.memo(
  ({ isAllSelected, onToggleAll }) => (
    <div className={styles.raSelectAllContainer}>
      <button onClick={onToggleAll} className={styles.raSelectAlltext}>
        <span>{`${isAllSelected ? 'Clear All' : 'Select All'}`}</span>
      </button>
    </div>
  ),
);

// --- Main Menu Component (Composer) ---

export const VesselDropdownMenu: React.FC<VesselDropdownMenuProps> = (props) => (
  <div className={styles.raDropdownMenu}>
    {props.isSearchBoxVisible && (
      <SearchInput searchTerm={props.searchTerm} onSearchChange={props.onSearchChange} />
    )}

    <VesselGroupList {...props} />

    {props.isSelectAllVisible && (
      <DropdownFooter isAllSelected={props.isAllSelected} onToggleAll={props.onToggleAll} />
    )}
  </div>
);
