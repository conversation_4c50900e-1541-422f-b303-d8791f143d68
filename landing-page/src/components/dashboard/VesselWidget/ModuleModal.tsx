import React from 'react';
import classNames from 'classnames';
import styles from '../styles/vessel-widget-scss/VesselModule.module.scss';

interface ModuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  sizeKey: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

/**
 * A generic, reusable modal component.
 */
export const ModuleModal: React.FC<ModuleModalProps> = ({ isOpen, onClose, sizeKey, children }) => {
  if (!isOpen) {
    return null;
  }

  const handleOverlayClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Close the modal only if the click is on the overlay itself, not its children
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <button
      type="button"
     className={classNames(styles['ra-modal-overlay'])}
      onClick={handleOverlayClick}
      aria-label="Close modal"
    >
       <div
        className={classNames(styles['ra-modal-content'], styles[`size-${sizeKey}`])}
        onClick={(e) => e.stopPropagation()}
        onKeyDown={(e) => {
          // Prevent propagation for keyboard events as well
          e.stopPropagation();
        }}
        tabIndex={0}
        role="dialog"
        aria-modal="true"
      >
        {children}
      </div>
    </button>
  );
};
