import React from 'react';
import { MailI<PERSON>, ArrowUp, ArrowDown, ChevronsUpDown } from 'lucide-react';
import classNames from 'classnames';
import { SortConfig, Vessel, VesselTableProps } from '../../../types/types';
import { useInfiniteScroll } from '../../../hooks/useInfiniteScroll';
import Spinner from './Spinner';
import styles from '../styles/vessel-widget-scss/VesselTable.module.scss';
import { ExternalLinkIcon } from './svgIcons';
import EmptyState from 'src/components/common/EmptyState';
import { EMPTY_STATE_MESSAGES } from 'src/utils/textConstants';

// New sub-components to be added to your file
const TruncatedCell: React.FC<{ value: string | number }> = ({ value }) => {
  const str = value.toString();
  const truncated = str.length > 40 ? `${str.slice(0, 40)}...` : str;
  return (
    <span className={styles['ra-vesselSecondColumnEntry']} title={str}>
      {truncated}
    </span>
  );
};

const StatusBadgeCell: React.FC<{ value: string | number }> = ({ value }) => {
  const statusClassMap: Record<string, string> = {
    Critical: styles['ra-status-critical'],
    Special: styles['ra-status-special'],
    Unassigned: styles['ra-status-unassigned'],
  };

  const statusClass = statusClassMap[value as string] || styles['ra-status-default'];

  return (
    <span className={classNames(styles['ra-statusBadge'], statusClass)}>{value.toString()}</span>
  );
};

const DataCellContent: React.FC<{
  cellStyleType: 'default' | 'conditional';
  columnIndex: number;
  value: string | number;
  badgeColors: string[];
}> = React.memo(({ cellStyleType, columnIndex, value, badgeColors }) => {
  if (cellStyleType === 'conditional') {
    switch (columnIndex) {
      case 0:
        return <TruncatedCell value={value} />;
      case 1:
        return <StatusBadgeCell value={value} />;
      default:
        return null;
    }
  }

  const getBadgeClass = (color: string) => {
    switch (color.toUpperCase()) {
      case '#FFF9E8':
        return styles['ra-badge-yellow'];
      case '#FAF2F5':
        return styles['ra-badge-red'];
      case '#E0E0E0':
      case '#808080':
      default:
        return styles['ra-badge-gray'];
    }
  };
  const badgeColor = badgeColors[columnIndex] || '#808080';
  const badgeClass = getBadgeClass(badgeColor);

  return (
    // <span className={styles['ra-badge']} style={badgeStyle}>
    <span className={classNames(styles['ra-badge'], badgeClass)}>{value.toString()}</span>
  );
});

export const getSortIcon = (headerKey: string, sortConfig: SortConfig) => {
  if (!sortConfig || sortConfig.key !== headerKey) {
    return (
      <ChevronsUpDown size={14} className={classNames(styles['ra-sortIcon'], styles.neutral)} />
    );
  }
  return sortConfig.direction === 'ascending' ? (
    <ArrowUp size={14} className={classNames(styles['ra-sortIcon'], styles.active)} />
  ) : (
    <ArrowDown size={14} className={classNames(styles['ra-sortIcon'], styles.active)} />
  );
};

const VesselTableHeader: React.FC<{
  headers: string[];
  sortConfig: SortConfig;
  onSort: (key: string) => void;
}> = React.memo(({ headers, sortConfig, onSort }) => {
  return (
    <thead className={styles['ra-tableHeader']}>
      <tr>
        {headers.map((header, index) => {
          return (
            <th
              key={header} // Use the unique header name as the key
              className={classNames({
                [styles['ra-vesselName-header-cell']]: index === 0,
                [styles['ra-task-header-cell']]: index === 1,
                [styles['ra-level-header-cell']]: index === 2,
              })}
              onClick={() => onSort(header)}
            >
              <div className={styles['ra-headerContent']}>{header}</div>
            </th>
          );
        })}
        <th
          key="actions"
          className={classNames(
            styles['ra-actionHeader'],
            styles.nonSortable,
            styles['ra-action-header-cell'],
          )}
        >
          <div className={styles['ra-headerContent']}>Action</div>
        </th>
      </tr>
    </thead>
  );
});

// --- Sub-component: Table Row (FIXED) ---
const VesselTableRow: React.FC<{
  vessel: Vessel;
  badgeColors: string[];
  onSendEmail: (vessel: Vessel) => void;
  onVesselClick: (vessel: Vessel) => void;
  cellStyleType: 'default' | 'conditional';
}> = React.memo(({ vessel, badgeColors, onSendEmail, onVesselClick, cellStyleType }) => (
  <tr className={styles['ra-tableRow']}>
    {/* Column 1: Vessel Name is rendered separately first */}
    <td className={styles['ra-vesselNameCell']}>
      <button onClick={() => onVesselClick(vessel)} className={styles['ra-vesselNameButton']}>
        {vessel.name}
      </button>
    </td>
    {/* Subsequent columns are mapped from the vesselData array */}
    {vessel.vesselData.map((value, i) => {
      return (
        <td key={`${vessel.vessel_id}-${vessel.vessel_ownership_id}`}>
          <DataCellContent
            cellStyleType={cellStyleType}
            columnIndex={i}
            value={value}
            badgeColors={badgeColors}
          />
        </td>
      );
    })}

    {/* Final Column: Actions */}
    <td className={styles['ra-emailCell']}>
      <div className={styles['ra-emailButtonWrapper']}>
        <button onClick={() => onSendEmail(vessel)} className={styles['ra-emailButton']}>
          {cellStyleType === 'conditional' ? <ExternalLinkIcon /> : <MailIcon size={20} />}
        </button>
        <div className={styles['ra-tooltip']}>
          {cellStyleType === 'conditional' ? 'View Details' : 'Send Email'}
        </div>
      </div>
    </td>
  </tr>
));

// --- Main Component ---
export default function VesselTable({
  vessels,
  tableHeaders,
  badgeColors,
  onSendEmail,
  onVesselClick,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  cellStyleType = 'default',
  sortConfig,
  onSort,
}: Readonly<VesselTableProps>) {
  const hasNextPage = pagination && pagination.page < pagination.totalPages;
  const dataHeaders = tableHeaders.filter((h) => h.toLowerCase() !== 'action');
  const { containerRef, handleScroll } = useInfiniteScroll({
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    dataLength: vessels?.length || 0,
  });

  const renderBody = () => {
    if (isLoading) {
      return (
        <tr key="loading">
          <td colSpan={dataHeaders.length + 1} className={styles['ra-statusCell']}>
            <Spinner />
          </td>
        </tr>
      );
    }
    if (!vessels || vessels.length === 0) {
      return (
        <tr key="no-results">
          <td colSpan={dataHeaders.length + 1} className={styles['ra-statusCell']}>
            No results found
          </td>
        </tr>
      );
    }
    return vessels.map((vessel) => (
      <VesselTableRow
        key={vessel.vessel_id}
        vessel={vessel}
        badgeColors={badgeColors}
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        cellStyleType={cellStyleType}
      />
    ));
  };

  return (
    <div ref={containerRef} onScroll={handleScroll} className={styles['ra-tableContainer']}>
      {vessels?.length === 0 ? (
        <EmptyState text={EMPTY_STATE_MESSAGES.NO_PENDING_RISK_ASSESSMENTS} />
      ) : (
        <table className={styles['ra-table']}>
          <VesselTableHeader headers={dataHeaders} sortConfig={sortConfig} onSort={onSort} />
          <tbody>{renderBody()}</tbody>
        </table>
      )}
      {isFetchingNextPage && (
        <div className={styles['ra-spinnerContainer']}>
          <Spinner />
        </div>
      )}
    </div>
  );
}
