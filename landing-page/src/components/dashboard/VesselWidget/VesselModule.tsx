import React, { useState, useMemo, useCallback } from 'react';
import { RotateCw } from 'lucide-react';
import classNames from 'classnames';
import { VesselModuleProps, Vessel, MultiVesselSelectConfig } from '../../../types/types';
import VesselTable from './VesselTable';
import VesselGrid from './VesselGrid';
import { VesselModuleHeader } from './VesselModuleHeader';
import { VesselSelectors } from './VesselSelectors';
import { ModuleModal } from './ModuleModal';
import styles from '../styles/vessel-widget-scss/VesselModule.module.scss';

// Define a type for our sorting configuration
type SortConfig = {
  key: string;
  direction: 'ascending' | 'descending';
} | null;

// Helper function to handle filtering logic
const filterVessels = (
  vesselsToFilter: Vessel[],
  selectStates: string[][],
  multiVesselSelects: MultiVesselSelectConfig[],
) => {
  let filteredData = vesselsToFilter;

  // Filter 1: Vessel Name
  const vesselNameSelections = selectStates[0];
  if (vesselNameSelections?.length > 0) {
    const allVesselOptions = multiVesselSelects[0]?.groups.flatMap((g) => g.vessels) || [];
    const selectedVesselIdentifiers = new Set(
      allVesselOptions
        .filter((opt) => vesselNameSelections.includes(opt.name))
        .map((opt) => `${opt.vessel_id}-${opt.vessel_ownership_id}`),
    );
    if (selectedVesselIdentifiers.size > 0) {
      filteredData = filteredData.filter((vessel) =>
        selectedVesselIdentifiers.has(`${vessel.vessel_id}-${vessel.vessel_ownership_id}`),
      );
    }
  }

  // Filter 2: Level RA
  const levelRaSelections = selectStates[1];
  if (levelRaSelections?.length > 0) {
    filteredData = filteredData.filter((vessel: Vessel) => {
      const vesselLevelOfRa = vessel.vesselData[1];
      return levelRaSelections.includes(vesselLevelOfRa as string);
    });
  }

  return filteredData;
};


export default function VesselModule({
  title,
  vessels, // The vessels array from API
  tabs,
  IsiconRenderVisible,
  IsenLargeIconVisible,
  IsVesselSelectVisible,
  IsAllTabVisible,
  multiVesselSelects = [],
  vesselSelectPosition = 'before',
  sizeKey,
  tableHeaders,
  badgeColors,
  onRefresh,
  gridComponent,
  defaultComponent = 'list',
  cellStyleType,
  lastUpdated,
  ...displayProps
}: Readonly<VesselModuleProps>) {
  const [viewMode, setViewMode] = useState<'list' | 'grid'>(defaultComponent);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectStates, setSelectStates] = useState<string[][]>(() =>
    multiVesselSelects.map(() => []),
  );

  const [sortConfig, setSortConfig] = useState<SortConfig>(null);

  const processedVessels = useMemo(() => {
    let data = vessels || [];
    data = filterVessels(data, selectStates, multiVesselSelects);
    return data;
  }, [vessels, selectStates, multiVesselSelects, sortConfig, tableHeaders]);

  const handleRefresh = useCallback(() => {
    onRefresh({ refetch: true }); // Pass the refetch param
  }, [onRefresh]);

  const handleSelectChange = useCallback((index: number, newSelected: string[]) => {
    setSelectStates((prevStates) => {
      const newStates = [...prevStates];
      newStates[index] = newSelected;
      return newStates;
    });
  }, []);

  const handleSort = useCallback((key: string) => {
    setSortConfig((currentSortConfig) => {
      let direction: 'ascending' | 'descending' = 'ascending';
      if (
        currentSortConfig &&
        currentSortConfig.key === key &&
        currentSortConfig.direction === 'ascending'
      ) {
        direction = 'descending';
      }
      return { key, direction };
    });
  }, []);

  // Determine if vessels data is available
  const hasVesselsData = vessels && vessels.length > 0;

  // Conditionally set IsiconRenderVisible and IsenLargeIconVisible
  const finalIsiconRenderVisible = hasVesselsData ? IsiconRenderVisible : false;
  const finalIsenLargeIconVisible = hasVesselsData ? IsenLargeIconVisible : false;

  const renderViewContent = (isModal: boolean) =>
    viewMode === 'list' ? (
      <VesselTable
        vessels={processedVessels}
        tableHeaders={tableHeaders}
        badgeColors={badgeColors}
        cellStyleType={cellStyleType}
        sortConfig={sortConfig}
        onSort={handleSort}
        {...displayProps}
      />
    ) : (
      <VesselGrid
        vessels={processedVessels}
        tableHeaders={tableHeaders}
        badgeColors={badgeColors}
        isModal={isModal}
        gridComponent={gridComponent}
        {...displayProps}
      />
    );

  const renderModuleCore = (isModal: boolean) => (
    <>
      <VesselModuleHeader
        title={title}
        viewMode={viewMode}
        isModal={isModal}
        IsiconRenderVisible={finalIsiconRenderVisible} // Use the conditionally set value
        IsenLargeIconVisible={finalIsenLargeIconVisible} // Use the conditionally set value
        onViewModeChange={setViewMode}
        onToggleModal={() => setIsModalOpen(!isModalOpen)}
      />
      <div className={classNames(styles['ra-last-updated-container'])}>
        <p className={classNames(styles['ra-last-updated-text'])}>
          Last Updated on:{' '}
          {(() => {
            const dateObj = new Date(lastUpdated);
            if (isNaN(dateObj.getTime())) {
              return null;
            }
            return `${dateObj.toLocaleDateString(undefined, {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
            })} ${dateObj.toLocaleTimeString(undefined, {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            })}`;
          })()}
          <RotateCw
            onClick={handleRefresh}
            size={16}
            className={classNames(styles['ra-refresh-icon'])}
          />
        </p>
      </div>
      {/* Render VesselSelectors only if there is vessel data or if it's explicitly visible */}
      {IsVesselSelectVisible && vesselSelectPosition === 'before' && vessels?.length > 0 && (
        <VesselSelectors
          multiVesselSelects={multiVesselSelects}
          selectStates={selectStates}
          onSelectChange={handleSelectChange}
        />
      )}
      {IsVesselSelectVisible && vesselSelectPosition === 'after' && vessels?.length > 0 && (
        <VesselSelectors
          multiVesselSelects={multiVesselSelects}
          selectStates={selectStates}
          onSelectChange={handleSelectChange}
        />
      )}
      <div
        className={classNames('ra-content-container', {
          'ra-content-container-modal': isModal,
          'ra-content-container-non-modal': !isModal,
        })}
      >
        {renderViewContent(isModal)}
      </div>
    </>
  );

  return (
    <>
      <div
        className={classNames(styles['ra-vessel-module-container'], {
          [styles['size-sm']]: sizeKey === 'sm',
          [styles['size-md']]: sizeKey === 'md',
          [styles['size-lg']]: sizeKey === 'lg',
        })}
      >
        {renderModuleCore(false)}
      </div>
      {isModalOpen && (
        <ModuleModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} sizeKey={sizeKey}>
          {renderModuleCore(true)}
        </ModuleModal>
      )}
    </>
  );
}
