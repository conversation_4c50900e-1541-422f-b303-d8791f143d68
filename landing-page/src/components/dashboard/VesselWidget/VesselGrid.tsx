import React from "react";
import { VesselGridProps } from "../../../types/types";
import { useInfiniteScroll } from "../../../hooks/useInfiniteScroll";
import Spinner from "./Spinner";
import "../styles/vessel-widget-scss/VesselGrid.scss";

export default function VesselGrid({
  vessels,
  tableHeaders,
  badgeColors,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  isModal,
  gridComponent = "bar",
  ...rest
}: Readonly<VesselGridProps>) {
  const hasNextPage = pagination && pagination.page < pagination.totalPages;

  const { containerRef, handleScroll } = useInfiniteScroll({
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    dataLength: vessels?.length || 0,
  });

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="spinner-container">
          <Spinner />
        </div>
      );
    }
    if (!vessels || vessels.length === 0) {
      return <div className="no-results-cell">No results found</div>;
    }

    // --- DYNAMIC RENDERING LOGIC ---
    if (gridComponent === "pie") {
      // If the prop says 'pie', render the Donut Chart via the adapter
    
    }

    if (gridComponent === "dashboard") {
      // If the prop is 'dashboard', render your DashboardComponent
      // Pass the necessary props like vessels, colors, etc.
     
     
    }

    return (
      <div className="vessel-bar-chart-wrapper">
      
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      onScroll={handleScroll}
      className="vessel-grid-root"
    >
      {renderContent()}
      {isFetchingNextPage && (
        <div className="loading-indicator">
          <Spinner />
        </div>
      )}
    </div>
  );
}
