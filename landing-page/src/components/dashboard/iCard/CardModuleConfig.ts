import { WidgetConstant } from './widget.constant';
import {
  fetchVesselOwnerships,
  getItinerariesForTable,
  getOfrStatsForTable,
  getSurveysAndCertsForTable,
  getVesselsRA,
} from '../../../services/vm-widget-service';
import {
  getDefiniciencyColumns,
  getRiskAssessmentColumns,
  getItineraryColumns,
  getOfrColumns,
  getSurveysAndCertsColumns,
} from './ColumnConfig';
import { vesselGroups2 } from 'src/services/__mocks__/card-module-config';
import { IProjectListResponse } from 'src/types/types';

enum RaLevel {
  //   ROUTINE = 'ROUTINE',
  //   LEVEL_1_RA = 'LEVEL_1_RA',
  SPECIAL = 'SPECIAL',
  CRITICAL = 'CRITICAL',
  Unassigned = 'Unassigned',
}

const levelMapping: Record<string, string> = {
  //   [RaLevel.ROUTINE]: 'Routine',
  //   [RaLevel.LEVEL_1_RA]: 'Level_1_RA',
  [RaLevel.SPECIAL]: 'Special',
  [RaLevel.CRITICAL]: 'Critical',
  [RaLevel.Unassigned]: 'Unassigned',
};

const raLevels = [
  //   RaLevel.ROUTINE,
  //   RaLevel.LEVEL_1_RA,
  RaLevel.SPECIAL,
  RaLevel.CRITICAL,
  RaLevel.Unassigned,
];

const types = [
  'Statutory',
  'Important',
  'Ancillary',
  'Non Conformity',
  'Observation',
  'Defect',
  'Technical Follow-up',
];

// Mock fetch function with infinte data for demonstration
const mockFetchFn1 = async (params: {
  page: number;
  limit: number;
  [key: string]: any;
}): Promise<any> => {
  console.log('Fetching with params:', params);
  await new Promise((resolve) => setTimeout(resolve, 1000));
  const mockData = Array.from({ length: params.limit }, (_, i) => ({
    type: types[i % types.length],
    vessel_ownership_id: i,
    vessel_name: `Vessel ${i + (params.page - 1) * params.limit}`,
    //these below 4 are responsible for deficiency
    name: `Vessel ${i + (params.page - 1) * params.limit}`,
    not_accepted_by_office: i,
    overdue: i,
    due_within_30_days: i,
    //till
    ra_level: levelMapping[raLevels[i % raLevels.length]], // Or provide mock levels e.g. "High", "Medium", etc.
    task_requiring_ra: `Task for Vessel ask for Vesseask for Vesseask for Vesse${i}`,
    id: i,
    vesselData: [i, i, i],
  }));
  return {
    data: mockData,
    pagination: {
      totalItems: 1,
      totalPages: 1,
      page: params.page,
      pageSize: params.limit,
    },
  };
};

// Define a static data array with 5 items.
const staticData = [
  {
    type: 'Non Conformity',
    vessel_ownership_id: 1,
    vessel_name: 'Vessel 1',
    name: 'Vessel 1',
    not_accepted_by_office: 5,
    overdue: 10,
    due_within_30_days: 15,
    ra_level: 'Critical',
    task_requiring_ra: 'Critical task for Vessel 1',
    id: 1,
    vesselData: [10, 5, 20],
  },
  {
    type: 'Observation',
    vessel_ownership_id: 2,
    vessel_name: 'Vessel 2',
    name: 'Vessel 2',
    not_accepted_by_office: 3,
    overdue: 8,
    due_within_30_days: 12,
    ra_level: 'Special',
    task_requiring_ra: 'Special task for Vessel 2',
    id: 2,
    vesselData: [8, 12, 5],
  },
  {
    type: 'Defect',
    vessel_ownership_id: 3,
    vessel_name: 'Vessel 3',
    name: 'Vessel 3',
    not_accepted_by_office: 1,
    overdue: 2,
    due_within_30_days: 3,
    ra_level: 'Unassigned',
    task_requiring_ra: 'Unassigned task for Vessel 3',
    id: 3,
    vesselData: [2, 3, 1],
  },
  {
    type: 'Technical Follow-up',
    vessel_ownership_id: 4,
    vessel_name: 'Vessel 4',
    name: 'Vessel 4',
    not_accepted_by_office: 6,
    overdue: 9,
    due_within_30_days: 11,
    ra_level: 'Critical',
    task_requiring_ra: 'Another critical task for Vessel 4',
    id: 4,
    vesselData: [9, 11, 6],
  },
  {
    type: 'Non Conformity',
    vessel_ownership_id: 5,
    vessel_name: 'Vessel 5',
    name: 'Vessel 5',
    not_accepted_by_office: 2,
    overdue: 4,
    due_within_30_days: 7,
    ra_level: 'Special',
    task_requiring_ra: 'A special task for Vessel 5',
    id: 5,
    vesselData: [4, 7, 2],
  },
];

// Modify mockFetchFn to return the static data
const mockFetchFn = async (params: {
  page: number;
  limit: number;
  [key: string]: any;
}): Promise<any> => {
  console.log('Fetching with params:', params);
  await new Promise((resolve) => setTimeout(resolve, 500)); // Shorten delay for better UX
  
  // Return the static array.
  // The pagination data is set to a single page with a total of 5 items.
  return {
    data: staticData,
    pagination: {
      totalItems: 5,
      totalPages: 1, // Indicate there is only one page
      page: 1,
      pageSize: 5,
    },
  };
};

const chartData = {
  openDeficiencies: {
    title: 'Open Deficiencies (Not accepted by office)',
    total: 763,
    data: [
      { label: 'Overdue', value: 65, color: 'red' },
      { label: 'Due within 30 days', value: 295, color: 'yellow' },
      { label: 'Others', value: 765, color: 'green' },
    ],
  },
  closedDeficiencies: {
    title: 'Closed Deficiencies (Accepted by office)',
    total: 362,
    data: [
      { label: 'High', value: 172, color: 'red' },
      { label: 'Medium', value: 450, color: 'yellow' },
      { label: 'Low', value: 505, color: 'green' },
    ],
  },
};

export const cardModuleConfigs: { [key: string]: any } = {
  [WidgetConstant.OWNER_FINANCIAL_REPORTING]: {
    title: 'Owner Financial Reporting',
    fetchFn1: getOfrStatsForTable,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    columns: getOfrColumns,
    sizeKey: 'md',
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
  },

  [WidgetConstant.ITINERARY_ETA]: {
    title: 'Itinerary (ETA)',
    fetchFn1: getItinerariesForTable,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px', // 200 or 300 or 350 should need to handle with an enum or interface to allow only these values
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    columns: getItineraryColumns,
    sizeKey: 'md',
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
  },

  [WidgetConstant.RISK_ASSESSMENT]: {
    title: 'Risk Assessment',
    fetchFn1: getVesselsRA,
    // Pass the function here, not the result of the call
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
      {
        placeholder: 'Level of R.A.',
        width: '300px',
        groups: vesselGroups2,
        isSearchBoxVisible: false,
        isSelectAllVisible: false,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    sizeKey: 'md',
    columns: getRiskAssessmentColumns,
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsAllTabVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
  },

  [WidgetConstant.DEFICIENCIES]: {
    title: 'Deficiencies',
    fetchFn1: mockFetchFn,
    // Pass the function here, not the result of the call
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['Non Conformity', 'Observation', 'Defect', 'Technical Follow-up'],
      tableHeaders: ['Overdue', 'Due within 30 Days', 'others'],
      badgeColors: ['#d80e61', '#fbc02d', '#27a527'],
      chartData: chartData,
    },
    sizeKey: 'md',
    columns: getDefiniciencyColumns,
    visibleConfig: {
      IsiconRenderVisible: true,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: true,
      IsAllTabVisible: true,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
    },
    componentView: {
      gridComponent: 'pie',
      defaultComponent: 'grid',
    },
  },

  [WidgetConstant.SURVEYS_CERTIFICATES]: {
    title: 'Surveys and Certificates',
    fetchFn1: getSurveysAndCertsForTable,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships,
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Overdue', 'Due within 30 Days', 'others'],
      badgeColors: ['#d80e61', '#fbc02d', '#27a527'],
    },
    sizeKey: 'md',
    columns: getSurveysAndCertsColumns,
    visibleConfig: {
      IsiconRenderVisible: true,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: true,
      IsAllTabVisible: true,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'grid',
    },
  },
};
