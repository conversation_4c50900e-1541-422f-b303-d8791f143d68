import React, { useMemo, useEffect, useState } from 'react';
import { useInfiniteQuery } from '../../../hooks/useInfiniteQuery';
import { Vessel, MultiSelectConfig, VesselGroup } from '../../../types/card-types';
import { cardModuleConfigs } from './CardModuleConfig';
import { CardModule } from '@paris2/styleguide';
import { WidgetConstant } from './widget.constant';

export interface CardModuleContainerProps {
  readonly configKey: string;
  onStatCardDataUpdate?: (data: { count: number; isLoading: boolean }) => void;
}

const vesselGroups2: VesselGroup[] = [
  {
    id: 2,
    title: '',
    vessels: [
      { vessel_id: -1, name: 'Unassigned' },
      { vessel_id: 3, name: 'Critical' },
      { vessel_id: 2, name: 'Special' },
      { vessel_id: 4, name: 'Level 1 RA' },
    ],
  },
];

export default function CardModuleContainer({
  configKey,
  onStatCardDataUpdate,
}: Readonly<CardModuleContainerProps>): JSX.Element {
  // Use the configKey to get the configuration from the centralized object
  const config = cardModuleConfigs[configKey];
  const [processedMultiVesselSelects, setProcessedMultiVesselSelects] = useState<
    MultiSelectConfig[] | undefined
  >(undefined);

  // New state to track the loading status of dropdowns
  const [isDropdownLoading, setIsDropdownLoading] = useState(false);

  if (!config) {
    console.error(`No configuration found for key: ${configKey}`);
    return <div>Error: Invalid configuration key provided.</div>;
  }

  const {
    title,
    fetchFn1,
    multiVesselSelects,
    staticData,
    visibleConfig,
    componentView,
    sizeKey,
    ...rest
  } = config;

  useEffect(() => {
    async function fetchGroups() {
      if (multiVesselSelects) {
        setIsDropdownLoading(true); // Set to true before fetching
        try {
          const processedSelects = await Promise.all(
            multiVesselSelects.map(async (selectConfig: any) => {
              if (typeof selectConfig.groups === 'function') {
                const groups = await selectConfig.groups();
                return { ...selectConfig, groups };
              }
              return selectConfig;
            }),
          );
          setProcessedMultiVesselSelects(processedSelects);
        } catch (error) {
          console.error('Failed to fetch dropdown groups:', error);
          // Handle error appropriately, e.g., set an error state
        } finally {
          setIsDropdownLoading(false); // Set to false after fetching is done
        }
      }
    }
    fetchGroups();
  }, [multiVesselSelects]);

  const { data, isLoading, isFetchingNextPage, fetchNextPage, refetch } = useInfiniteQuery(
    fetchFn1,
    { limit: 1000 },
  );

  useEffect(() => {
    if (onStatCardDataUpdate) {
      switch (configKey) {
        case WidgetConstant.DEFICIENCIES:
          // Calculate total overdue from all items
          const totalOverdue =
            data?.data?.reduce((total: number, item: any) => {
              // Convert the string to number and add to total
              return total + (parseInt(item.overdue) || 0);
            }, 0) || 0;

          onStatCardDataUpdate({
            count: totalOverdue,
            isLoading,
          });
          break;

        case WidgetConstant.RISK_ASSESSMENT:
          // Count items where ra_level is "Unassigned"
          const unassignedRiskCount =
            data?.data?.filter((item: any) => item?.ra_level === 'Unassigned').length || 0;

          onStatCardDataUpdate({
            count: unassignedRiskCount,
            isLoading,
          });
          break;

        case WidgetConstant.SURVEYS_CERTIFICATES:
          // Calculate total due_within_30_days from all items
          const totalDueWithin30Days =
            data?.data?.reduce((total: number, item: any) => {
              // Convert the string to number and add to total
              return total + (parseInt(item.due_within_30_days) || 0);
            }, 0) || 0;

          onStatCardDataUpdate({
            count: totalDueWithin30Days,
            isLoading,
          });
          break;

        default:
          break;
      }
    }
  }, [data, isLoading]);

  const handleSendEmail = (vessel: Vessel) => {
    if (!vessel.risk_id) return;
    const url = `https://paris2-dev2.fleetship.com/risk-assessment/approval/${vessel.risk_id}`;
    window.open(url, '_blank');
  };

  const handleVesselClick = (vessel: Vessel) => {
    if (!vessel.vessel_ownership_id) return;
    const url = `https://paris2-dev2.fleetship.com/vessel/ownership/details/${vessel.vessel_ownership_id}`;
    window.open(url, '_blank');
  };

  const vessels = useMemo(() => data?.data ?? [], [data]);
  const pagination = data?.pagination ?? {
    totalItems: 0,
    totalPages: 0,
    page: 0,
    pageSize: 0,
  };

  // Memoize columns to prevent infinite re-renders - this ensures stable reference
  const stableColumns = useMemo(() => {
    // If columns are passed in config, use them; otherwise use empty array
    return config.columns || [];
  }, [config.columns]);

  const dropdownsToRender = useMemo(() => {
    // If loading, use a placeholder based on the initial config
    if (isDropdownLoading) {
      return config.multiVesselSelects.map((selectConfig: any) => ({
        ...selectConfig,
        groups: vesselGroups2,
      }));
    }
    // Otherwise, use the fetched data
    return processedMultiVesselSelects || [];
  }, [isDropdownLoading, config.multiVesselSelects, processedMultiVesselSelects]);

  return (
    <CardModule
      title={title}
      vessels={vessels}
      multiVesselSelects={dropdownsToRender}
      pagination={pagination}
      isLoading={isLoading}
      isFetchingNextPage={isFetchingNextPage}
      fetchNextPage={fetchNextPage}
      onRefresh={refetch}
      onSendEmail={handleSendEmail}
      onVesselClick={handleVesselClick}
      staticData={staticData}
      visibleConfig={visibleConfig}
      componentView={componentView}
      sizeKey={sizeKey}
      columns={stableColumns}
      {...rest}
    />
  );
}
