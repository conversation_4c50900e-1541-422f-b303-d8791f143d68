import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { TimezoneDropdown } from '../common/TimezoneDropdown';

jest.mock('react-bootstrap-typeahead', () => {
  return {
    __esModule: true,
    Typeahead: jest.fn(({ onChange, options }) => (
      <select data-testid="timezone-dropdown" onChange={e => onChange([{ city: e.target.value }])}>
        {options.map(opt => (
          <option key={opt.city} value={opt.city}>
            {opt.city}
          </option>
        ))}
      </select>
    )),
  };
});

describe('TimezoneDropdown Component', () => {
  const mockOnChange = jest.fn();
  const optionsList = [{ city: 'New York' }, { city: 'London' }, { city: 'Hong Kong' }];

  it('renders the dropdown with label', () => {
    render(
      <TimezoneDropdown
        optionsList={optionsList}
        label="Select Timezone"
        onChange={mockOnChange}
      />,
    );
    expect(screen.getByText('Select Timezone')).toBeInTheDocument();
    expect(screen.getByTestId('timezone-dropdown')).toBeInTheDocument();
  });

  it('calls onChange when an option is selected', () => {
    render(
      <TimezoneDropdown
        optionsList={optionsList}
        label="Select Timezone"
        onChange={mockOnChange}
      />,
    );
    const dropdown = screen.getByTestId('timezone-dropdown');
    fireEvent.change(dropdown, { target: { value: 'London' } });
    expect(mockOnChange).toHaveBeenCalledWith({ city: 'London' });
  });

  it("formats 'Hong Kong' as 'Hong Kong'", () => {
    render(
      <TimezoneDropdown
        optionsList={optionsList}
        label="Select Timezone"
        onChange={mockOnChange}
      />,
    );
    expect(screen.getByText('Hong Kong')).toBeInTheDocument();
  });
});
