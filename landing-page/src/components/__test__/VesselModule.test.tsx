import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import VesselModule from '../dashboard/VesselWidget/VesselModule';
import { Vessel, MultiVesselSelectConfig } from '../../types/types';

// Mock the child components
jest.mock('../dashboard/VesselWidget/VesselTable', () => {
  return function MockVesselTable({ vessels, onSort, sortConfig, onSendEmail, onVesselClick }: any) {
    return (
      <div data-testid="vessel-table">
        <div>Vessels: {vessels?.length || 0}</div>
        <div>Sort Key: {sortConfig?.key || 'none'}</div>
        <div>Sort Direction: {sortConfig?.direction || 'none'}</div>
        <button onClick={() => onSort('Name')}>Sort by Name</button>
        <button onClick={() => onSendEmail(vessels[0])}>Send Email</button>
        <button onClick={() => onVesselClick(vessels[0])}>Click Vessel</button>
      </div>
    );
  };
});

jest.mock('../dashboard/VesselWidget/VesselGrid', () => {
  return function MockVesselGrid({ vessels, isModal }: any) {
    return (
      <div data-testid="vessel-grid">
        <div>Vessels: {vessels?.length || 0}</div>
        <div>Is Modal: {isModal.toString()}</div>
      </div>
    );
  };
});

jest.mock('../dashboard/VesselWidget/VesselModuleHeader', () => ({
  VesselModuleHeader: ({ title, viewMode, isModal, onViewModeChange, onToggleModal }: any) => (
    <div data-testid="vessel-module-header">
      <div>Title: {title}</div>
      <div>View Mode: {viewMode}</div>
      <div>Is Modal: {isModal.toString()}</div>
      <button onClick={() => onViewModeChange('grid')}>Switch to Grid</button>
      <button onClick={() => onViewModeChange('list')}>Switch to List</button>
      <button onClick={onToggleModal}>Toggle Modal</button>
    </div>
  ),
}));

jest.mock('../dashboard/VesselWidget/VesselSelectors', () => ({
  VesselSelectors: ({ multiVesselSelects, selectStates, onSelectChange }: any) => (
    <div data-testid="vessel-selectors">
      <div>Selects: {multiVesselSelects?.length || 0}</div>
      <div>States: {selectStates?.length || 0}</div>
      <button onClick={() => onSelectChange(0, ['Vessel Alpha'])}>Change Vessel Selection</button>
      <button onClick={() => onSelectChange(1, ['Critical'])}>Change RA Selection</button>
      <button onClick={() => onSelectChange(0, [])}>Clear Vessel Selection</button>
      <button onClick={() => onSelectChange(1, [])}>Clear RA Selection</button>
    </div>
  ),
}));

jest.mock('../dashboard/VesselWidget/ModuleModal', () => ({
  ModuleModal: ({ isOpen, onClose, children }: any) => (
    isOpen ? (
      <div data-testid="module-modal">
        <button onClick={onClose}>Close Modal</button>
        {children}
      </div>
    ) : null
  ),
}));

jest.mock('lucide-react', () => ({
  RotateCw: ({ onClick, className }: any) => (
    <div data-testid="rotate-icon" className={className} onClick={onClick}>
      Refresh
    </div>
  ),
}));

describe('VesselModule', () => {
  const mockVessels: Vessel[] = [
    {
      name: 'Vessel Alpha',
      vesselData: [{ status: 'active' }],
      type: 'cargo',
      vessel_ownership_id: 101,
      risk_id: 1,
      vessel_id: 1,
    },
    {
      name: 'Vessel Beta',
      vesselData: [{ status: 'inactive' }],
      type: 'tanker',
      vessel_ownership_id: 102,
      risk_id: 2,
      vessel_id: 2,
    },
  ];

  const mockMultiVesselSelects: MultiVesselSelectConfig[] = [
    {
      placeholder: 'All Vessels',
      width: '300px',
      groups: [
        {
          id: 1,
          title: 'Group 1',
          vessels: [
            { vessel_id: 1, name: 'Vessel A', vessel_ownership_id: 101 },
          ],
        },
      ],
      isSearchBoxVisible: true,
      isSelectAllVisible: true,
    },
  ];

  const defaultProps = {
    title: 'Test Vessel Module',
    vessels: mockVessels,
    tabs: [],
    IsiconRenderVisible: true,
    IsenLargeIconVisible: true,
    IsVesselSelectVisible: true,
    IsAllTabVisible: true,
    multiVesselSelects: mockMultiVesselSelects,
    vesselSelectPosition: 'before' as const,
    containerSize: { width: '100%', height: '500px' },
    modalSize: { width: '80%', height: '80%' },
    tableHeaders: ['Name', 'Status', 'Type'],
    badgeColors: ['#ff0000', '#00ff00', '#0000ff'],
    onRefresh: jest.fn(),
    gridComponent: 'bar' as const,
    defaultComponent: 'list' as const,
    cellStyleType: 'default' as const,
    lastUpdated: '2021-01-01T00:00:00Z',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render with default props', () => {
    render(<VesselModule {...defaultProps} />);
    
    expect(screen.getByTestId('vessel-module-header')).toBeInTheDocument();
    expect(screen.getByText('Title: Test Vessel Module')).toBeInTheDocument();
    expect(screen.getByText('View Mode: list')).toBeInTheDocument();
  });

  it('should render vessel table in list mode', () => {
    render(<VesselModule {...defaultProps} defaultComponent="list" />);
    
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-grid')).not.toBeInTheDocument();
  });

  it('should render vessel grid in grid mode', () => {
    render(<VesselModule {...defaultProps} defaultComponent="grid" />);
    
    expect(screen.getByTestId('vessel-grid')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-table')).not.toBeInTheDocument();
  });

  it('should switch view modes correctly', () => {
    render(<VesselModule {...defaultProps} />);
    
    // Initially in list mode
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
    
    // Switch to grid mode
    fireEvent.click(screen.getByText('Switch to Grid'));
    expect(screen.getByTestId('vessel-grid')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-table')).not.toBeInTheDocument();
    
    // Switch back to list mode
    fireEvent.click(screen.getByText('Switch to List'));
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-grid')).not.toBeInTheDocument();
  });

  it('should toggle modal correctly', () => {
    render(<VesselModule {...defaultProps} />);
    
    // Modal should not be open initially
    expect(screen.queryByTestId('module-modal')).not.toBeInTheDocument();
    
    // Open modal
    fireEvent.click(screen.getByText('Toggle Modal'));
    expect(screen.getByTestId('module-modal')).toBeInTheDocument();
    
    // Close modal
    fireEvent.click(screen.getByText('Close Modal'));
    expect(screen.queryByTestId('module-modal')).not.toBeInTheDocument();
  });

  it('should handle refresh functionality', async () => {
    const onRefresh = jest.fn();
    render(<VesselModule {...defaultProps} onRefresh={onRefresh} />);
    
    const refreshIcon = screen.getByTestId('rotate-icon');
    fireEvent.click(refreshIcon);
    
    expect(onRefresh).toHaveBeenCalledTimes(1);
  });

  it('should render vessel selectors when visible', () => {
    render(<VesselModule {...defaultProps} IsVesselSelectVisible={true} />);
    
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('should not render vessel selectors when not visible', () => {
    render(<VesselModule {...defaultProps} IsVesselSelectVisible={false} />);
    
    expect(screen.queryByTestId('vessel-selectors')).not.toBeInTheDocument();
  });

  it('should handle vessel selector position before', () => {
    render(
      <VesselModule 
        {...defaultProps} 
        IsVesselSelectVisible={true}
        vesselSelectPosition="before"
      />
    );
    
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('should handle vessel selector position after', () => {
    render(
      <VesselModule 
        {...defaultProps} 
        IsVesselSelectVisible={true}
        vesselSelectPosition="after"
      />
    );
    
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('should handle sorting functionality', () => {
    render(<VesselModule {...defaultProps} />);
    
    const sortButton = screen.getByText('Sort by Name');
    fireEvent.click(sortButton);
    
    // Check that sort state is updated
    expect(screen.getByText('Sort Key: Name')).toBeInTheDocument();
    expect(screen.getByText('Sort Direction: ascending')).toBeInTheDocument();
  });

  it('should handle vessel selection changes', () => {
    render(<VesselModule {...defaultProps} />);

    const changeSelectionButton = screen.getByText('Change Vessel Selection');
    fireEvent.click(changeSelectionButton);

    // Should not crash and should handle the selection change
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('should display last updated time', () => {
    render(<VesselModule {...defaultProps} />);
    
    expect(screen.getByText(/Last Updated on:/)).toBeInTheDocument();
  });

  it('should handle empty vessels array', () => {
    render(<VesselModule {...defaultProps} vessels={[]} />);
    
    expect(screen.getByText('Vessels: 0')).toBeInTheDocument();
  });

  it('should handle undefined vessels', () => {
    render(<VesselModule {...defaultProps} vessels={undefined} />);
    
    expect(screen.getByText('Vessels: 0')).toBeInTheDocument();
  });

  it('should conditionally show icons based on vessel data', () => {
    // With vessels data
    const { unmount } = render(<VesselModule {...defaultProps} vessels={mockVessels} />);
    expect(screen.getByTestId('vessel-module-header')).toBeInTheDocument();
    unmount();

    // Without vessels data
    render(<VesselModule {...defaultProps} vessels={[]} />);
    expect(screen.getByTestId('vessel-module-header')).toBeInTheDocument();
  });

  it('should handle modal content rendering', () => {
    render(<VesselModule {...defaultProps} />);
    
    // Open modal
    fireEvent.click(screen.getByText('Toggle Modal'));
    
    const modal = screen.getByTestId('module-modal');
    expect(modal).toBeInTheDocument();
    
    // Modal should contain the same content as main view
    expect(modal).toHaveTextContent('Title: Test Vessel Module');
  });

  it('should handle different grid components', () => {
    render(<VesselModule {...defaultProps} gridComponent="pie" defaultComponent="grid" />);
    
    expect(screen.getByTestId('vessel-grid')).toBeInTheDocument();
  });

  it('should handle cell style types', () => {
    render(<VesselModule {...defaultProps} cellStyleType="conditional" />);
    
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
  });


  it('should handle tabs prop', () => {
    const tabs = ['Tab 1', 'Tab 2'];
    render(<VesselModule {...defaultProps} tabs={tabs} />);
    
    expect(screen.getByTestId('vessel-module-header')).toBeInTheDocument();
  });

  it('should handle badge colors', () => {
    const badgeColors = ['#red', '#green', '#blue'];
    render(<VesselModule {...defaultProps} badgeColors={badgeColors} />);

    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
  });

  describe('Vessel Filtering', () => {
    const vesselsWithData: Vessel[] = [
      {
        name: 'Vessel Alpha',
        vesselData: ['data1', 'Critical', 'Approved'],
        type: 'cargo',
        vessel_ownership_id: 101,
        risk_id: 1,
        vessel_id: 1,
      },
      {
        name: 'Vessel Beta',
        vesselData: ['data2', 'Special', 'Pending'],
        type: 'tanker',
        vessel_ownership_id: 102,
        risk_id: 2,
        vessel_id: 2,
      },
      {
        name: 'Vessel Gamma',
        vesselData: ['data3', 'Unassigned', 'Rejected'],
        type: 'bulk',
        vessel_ownership_id: 103,
        risk_id: 3,
        vessel_id: 3,
      },
    ];

    const multiSelectWithOptions: MultiVesselSelectConfig[] = [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: [
          {
            id: 1,
            title: 'Group 1',
            vessels: [
              { vessel_id: 1, name: 'Vessel Alpha', vessel_ownership_id: 101 },
              { vessel_id: 2, name: 'Vessel Beta', vessel_ownership_id: 102 },
            ],
          },
        ],
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
      {
        placeholder: 'Level RA',
        width: '200px',
        groups: [
          {
            id: 2,
            title: 'RA Levels',
            vessels: [
              { vessel_id: 1, name: 'Critical', vessel_ownership_id: 101 },
              { vessel_id: 2, name: 'Special', vessel_ownership_id: 102 },
            ],
          },
        ],
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ];

    it('should filter vessels by vessel name selection', () => {
      render(
        <VesselModule
          {...defaultProps}
          vessels={vesselsWithData}
          multiVesselSelects={multiSelectWithOptions}
        />
      );

      // Initially should show all vessels
      expect(screen.getByText('Vessels: 3')).toBeInTheDocument();

      // Simulate vessel selection change to filter by specific vessel
      const changeVesselSelectionButton = screen.getByText('Change Vessel Selection');
      fireEvent.click(changeVesselSelectionButton);

      // After filtering, should show filtered results
      expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
    });

    it('should filter vessels by level RA selection', () => {
      render(
        <VesselModule
          {...defaultProps}
          vessels={vesselsWithData}
          multiVesselSelects={multiSelectWithOptions}
        />
      );

      // Initially should show all vessels
      expect(screen.getByText('Vessels: 3')).toBeInTheDocument();

      // Simulate RA level selection change
      const changeRASelectionButton = screen.getByText('Change RA Selection');
      fireEvent.click(changeRASelectionButton);

      expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
    });

    it('should clear vessel name filter', () => {
      render(
        <VesselModule
          {...defaultProps}
          vessels={vesselsWithData}
          multiVesselSelects={multiSelectWithOptions}
        />
      );

      // Apply filter first
      const changeVesselSelectionButton = screen.getByText('Change Vessel Selection');
      fireEvent.click(changeVesselSelectionButton);

      // Then clear filter
      const clearVesselSelectionButton = screen.getByText('Clear Vessel Selection');
      fireEvent.click(clearVesselSelectionButton);

      expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
    });

    it('should clear RA level filter', () => {
      render(
        <VesselModule
          {...defaultProps}
          vessels={vesselsWithData}
          multiVesselSelects={multiSelectWithOptions}
        />
      );

      // Apply filter first
      const changeRASelectionButton = screen.getByText('Change RA Selection');
      fireEvent.click(changeRASelectionButton);

      // Then clear filter
      const clearRASelectionButton = screen.getByText('Clear RA Selection');
      fireEvent.click(clearRASelectionButton);

      expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
    });

    it('should handle filtering with no matching vessels', () => {
      const multiSelectWithNoMatches: MultiVesselSelectConfig[] = [
        {
          placeholder: 'All Vessels',
          width: '300px',
          groups: [
            {
              id: 1,
              title: 'Group 1',
              vessels: [
                { vessel_id: 999, name: 'Non-existent Vessel', vessel_ownership_id: 999 },
              ],
            },
          ],
          isSearchBoxVisible: true,
          isSelectAllVisible: true,
        },
      ];

      render(
        <VesselModule
          {...defaultProps}
          vessels={vesselsWithData}
          multiVesselSelects={multiSelectWithNoMatches}
        />
      );

      expect(screen.getByText('Vessels: 3')).toBeInTheDocument();
    });

    it('should handle empty vessel selections', () => {
      render(
        <VesselModule
          {...defaultProps}
          vessels={vesselsWithData}
          multiVesselSelects={[]}
        />
      );

      expect(screen.getByText('Vessels: 3')).toBeInTheDocument();
    });
  });

  describe('Vessel Sorting', () => {
    const vesselsForSorting: Vessel[] = [
      {
        name: 'Vessel Charlie',
        vesselData: ['data3', 'Critical'],
        type: 'cargo',
        vessel_ownership_id: 103,
        risk_id: 3,
        vessel_id: 3,
      },
      {
        name: 'Vessel Alpha',
        vesselData: ['data1', 'Special'],
        type: 'tanker',
        vessel_ownership_id: 101,
        risk_id: 1,
        vessel_id: 1,
      },
      {
        name: 'Vessel Beta',
        vesselData: ['data2', 'Unassigned'],
        type: 'bulk',
        vessel_ownership_id: 102,
        risk_id: 2,
        vessel_id: 2,
      },
    ];

    it('should sort vessels by name ascending', () => {
      render(
        <VesselModule
          {...defaultProps}
          vessels={vesselsForSorting}
          tableHeaders={['Name', 'Data', 'Status']}
        />
      );

      // Click sort by Name
      fireEvent.click(screen.getByText('Sort by Name'));
      expect(screen.getByText('Sort Key: Name')).toBeInTheDocument();
      expect(screen.getByText('Sort Direction: ascending')).toBeInTheDocument();

      // Click again to sort descending
      fireEvent.click(screen.getByText('Sort by Name'));
      expect(screen.getByText('Sort Direction: descending')).toBeInTheDocument();
    });

    it('should sort vessels by data column', () => {
      render(
        <VesselModule
          {...defaultProps}
          vessels={vesselsForSorting}
          tableHeaders={['Name', 'Data', 'Status']}
        />
      );

      // Mock sorting by data column (index 1)
      const mockVesselTable = require('../dashboard/VesselWidget/VesselTable');

      expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
    });

    it('should handle sorting with equal values', () => {
      const vesselsWithEqualData: Vessel[] = [
        {
          name: 'Vessel A',
          vesselData: ['same', 'Critical'],
          type: 'cargo',
          vessel_ownership_id: 101,
          risk_id: 1,
          vessel_id: 1,
        },
        {
          name: 'Vessel B',
          vesselData: ['same', 'Critical'],
          type: 'tanker',
          vessel_ownership_id: 102,
          risk_id: 2,
          vessel_id: 2,
        },
      ];

      render(
        <VesselModule
          {...defaultProps}
          vessels={vesselsWithEqualData}
          tableHeaders={['Name', 'Data', 'Status']}
        />
      );

      expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
    });
  });
  it('should sort vessels by name ascending', () => {
    const vesselsForSorting = [
  {
    name: 'Vessel Charlie',
    vesselData: ['data3', 'Critical'],
    type: 'cargo',
    vessel_ownership_id: 103,
    risk_id: 3,
    vessel_id: 3,
  },
  // ... more vessels
];
  render(
    <VesselModule
      {...defaultProps}
      vessels={vesselsForSorting}
      tableHeaders={['Name', 'Data', 'Status']}
    />
  );

  // Click sort by Name
  fireEvent.click(screen.getByText('Sort by Name'));
  expect(screen.getByText('Sort Key: Name')).toBeInTheDocument();
  expect(screen.getByText('Sort Direction: ascending')).toBeInTheDocument();

  // Click again to sort descending
  fireEvent.click(screen.getByText('Sort by Name'));
  expect(screen.getByText('Sort Direction: descending')).toBeInTheDocument();
});
});
