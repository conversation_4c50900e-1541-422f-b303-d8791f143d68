import React from 'react';
import { render, screen } from '@testing-library/react';
import { Divider } from '../common/Divider';

describe('Divider Component', () => {
  it('renders with default height and without dashed class', () => {
    render(<Divider />);
    const divider = screen.getByTestId('divider-test-id');
    expect(divider).toBeInTheDocument();
    expect(divider).not.toHaveClass('dashed');
    expect(divider).toHaveStyle({ paddingTop: '1%', paddingBottom: '1%' });
  });

  it('renders with custom height', () => {
    render(<Divider height="5" />);
    const divider = screen.getByTestId('divider-test-id');
    expect(divider).toHaveStyle({ paddingTop: '5%', paddingBottom: '5%' });
  });

  it('renders with dashed class when dashed prop is true', () => {
    render(<Divider dashed={true} />);
    const divider = screen.getByTestId('divider-test-id');
    expect(divider).toHaveClass('dashed');
  });

  it('does not render dashed class when dashed prop is false', () => {
    render(<Divider dashed={false} />);
    const divider = screen.getByTestId('divider-test-id');
    expect(divider).not.toHaveClass('dashed');
  });
});
