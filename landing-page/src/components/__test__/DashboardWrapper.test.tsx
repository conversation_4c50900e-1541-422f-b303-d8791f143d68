import React from 'react';
import { render, screen } from '@testing-library/react';
import { DashboardWrapper } from '../common/DashboardWrapper';
import TableauReport from 'tableau-react';
import { getTableauOptions } from '../../utils/dashboard';

jest.mock('tableau-react', () =>
  jest.fn(() => <div data-testid="tableau-report">Tableau Report</div>),
);
jest.mock('../dashboard/AlertGuide', () =>
  jest.fn(({ caption }) => <div data-testid="alert-guide">{caption}</div>),
);
jest.mock('../../utils/dashboard', () => ({
  getTableauOptions: jest.fn(() => ({ someOption: true })),
}));

describe('DashboardWrapper Component', () => {
  const defaultProps = {
    token: 'valid_token',
    query: 'some_query',
    filters: { filter1: 'value1' },
    parameters: { param1: 'value1' },
    url: 'https://tableau.url',
  };

  it('renders TableauReport when token is valid', () => {
    render(<DashboardWrapper {...defaultProps} />);
    expect(screen.getByTestId('tableau-report')).toBeInTheDocument();
  });

  it('renders AlertGuide when token is in error state', () => {
    render(<DashboardWrapper {...defaultProps} token="error" />);
    expect(screen.getByTestId('alert-guide')).toHaveTextContent('Unable to Load');
  });

  it('passes correct props to TableauReport', () => {
    render(<DashboardWrapper {...defaultProps} />);
    expect(TableauReport).toHaveBeenCalledWith(
      expect.objectContaining({
        url: defaultProps.url,
        options: getTableauOptions(),
        token: defaultProps.token,
        query: defaultProps.query,
        filters: defaultProps.filters,
        parameters: defaultProps.parameters,
      }),
      expect.anything(),
    );
  });
});
