import React from 'react';
import { render, screen } from '@testing-library/react';
import { AppLoader } from '../common/AppLoader';

jest.mock('../../StyleGuide', () => ({
  ErrorPage: jest.fn(() => <div data-testid="error-page">Error Page</div>),
}));

jest.mock('../common/Loader', () => ({
  Loader: jest.fn(() => <div data-testid="loader">Loading...</div>),
}));

describe('AppLoader Component', () => {
  it('renders the Loader component when hasError is false', () => {
    render(<AppLoader hasError={false} />);
    expect(screen.getByTestId('loader')).toBeInTheDocument();
    expect(screen.queryByTestId('error-page')).not.toBeInTheDocument();
  });

  it('renders the ErrorPage component when hasError is true', () => {
    render(<AppLoader hasError={true} />);
    expect(screen.getByTestId('error-page')).toBeInTheDocument();
    expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
  });
});
