import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { EnlargeIcon, ExternalLinkIcon } from '../dashboard/VesselWidget/svgIcons';

describe('svgIcons', () => {
  describe('EnlargeIcon', () => {
    it('should render with default props', () => {
      render(<EnlargeIcon data-testid="enlarge-icon" />);
      const icon = screen.getByTestId('enlarge-icon');
      
      expect(icon).toBeInTheDocument();
      expect(icon.tagName).toBe('svg');
      expect(icon).toHaveAttribute('width', '20');
      expect(icon).toHaveAttribute('height', '21');
      expect(icon).toHaveAttribute('viewBox', '0 0 20 21');
      expect(icon).toHaveAttribute('fill', 'none');
    });

    it('should spread custom props correctly', () => {
      render(
        <EnlargeIcon 
          data-testid="enlarge-icon"
          className="custom-class"
          onClick={() => {}}
          aria-label="Enlarge view"
          style={{ color: 'red' }}
        />
      );
      const icon = screen.getByTestId('enlarge-icon');
      
      expect(icon).toHaveClass('custom-class');
      expect(icon).toHaveAttribute('aria-label', 'Enlarge view');
      expect(icon).toHaveStyle('color: red');
    });

    it('should render the correct path element', () => {
      render(<EnlargeIcon data-testid="enlarge-icon" />);
      const icon = screen.getByTestId('enlarge-icon');
      const path = icon.querySelector('path');
      
      expect(path).toBeInTheDocument();
      expect(path).toHaveAttribute('fill', '#1F4A70');
      expect(path).toHaveAttribute('d', 'M17.6929 3.94936V8.23187H19V1.7327H12.5008V3.03977H16.7833L2.30707 17.516V13.2335H1V19.7327H7.49918V18.4256H3.21667L17.6929 3.94936Z');
    });

    it('should handle custom width and height', () => {
      render(<EnlargeIcon data-testid="enlarge-icon" width="30" height="31" />);
      const icon = screen.getByTestId('enlarge-icon');
      
      expect(icon).toHaveAttribute('width', '30');
      expect(icon).toHaveAttribute('height', '31');
    });
  });

  describe('ExternalLinkIcon', () => {
    it('should render with default props', () => {
      render(<ExternalLinkIcon data-testid="external-link-icon" />);
      const icon = screen.getByTestId('external-link-icon');
      
      expect(icon).toBeInTheDocument();
      expect(icon.tagName).toBe('svg');
      expect(icon).toHaveAttribute('width', '20');
      expect(icon).toHaveAttribute('height', '21');
      expect(icon).toHaveAttribute('viewBox', '0 0 20 21');
      expect(icon).toHaveAttribute('fill', 'none');
    });

    it('should spread custom props correctly', () => {
      const handleClick = jest.fn();
      render(
        <ExternalLinkIcon 
          data-testid="external-link-icon"
          className="external-class"
          onClick={handleClick}
          aria-label="Open external link"
          title="External Link"
        />
      );
      const icon = screen.getByTestId('external-link-icon');
      
      expect(icon).toHaveClass('external-class');
      expect(icon).toHaveAttribute('aria-label', 'Open external link');
      expect(icon).toHaveAttribute('title', 'External Link');
    });

    it('should render the correct path element', () => {
      render(<ExternalLinkIcon data-testid="external-link-icon" />);
      const icon = screen.getByTestId('external-link-icon');
      const path = icon.querySelector('path');
      
      expect(path).toBeInTheDocument();
      expect(path).toHaveAttribute('fill', '#1F4A70');
    });

    it('should handle all SVG attributes', () => {
      render(
        <ExternalLinkIcon 
          data-testid="external-link-icon"
          xmlns="http://www.w3.org/2000/svg"
          role="img"
        />
      );
      const icon = screen.getByTestId('external-link-icon');
      
      expect(icon).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
      expect(icon).toHaveAttribute('role', 'img');
    });

    it('should be accessible', () => {
      const { container } = render(<ExternalLinkIcon aria-hidden="true" />);
      const icon = container.querySelector('svg[aria-hidden="true"]');

      expect(icon).toHaveAttribute('aria-hidden', 'true');
    });
  });

  describe('Icon interactions', () => {
    it('should handle click events on EnlargeIcon', () => {
      const handleClick = jest.fn();
      render(<EnlargeIcon data-testid="enlarge-icon" onClick={handleClick} />);
      const icon = screen.getByTestId('enlarge-icon');

      fireEvent.click(icon);
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('should handle click events on ExternalLinkIcon', () => {
      const handleClick = jest.fn();
      render(<ExternalLinkIcon data-testid="external-link-icon" onClick={handleClick} />);
      const icon = screen.getByTestId('external-link-icon');

      fireEvent.click(icon);
      expect(handleClick).toHaveBeenCalledTimes(1);
    });
  });
});
