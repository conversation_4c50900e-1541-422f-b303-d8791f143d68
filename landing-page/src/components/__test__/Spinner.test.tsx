import React from 'react';
import { render, screen } from '@testing-library/react';
import Spinner from '../dashboard/VesselWidget/Spinner';

describe('Spinner', () => {
  it('should render with default alignClass', () => {
    render(<Spinner />);
    
    const spinnerContainer = screen.getByText('Loading...');
    expect(spinnerContainer).toBeInTheDocument();
  });

  it('should render with custom alignClass', () => {
    render(<Spinner alignClass="custom-align-class" />);
    
    const spinnerContainer = screen.getByText('Loading...');
    expect(spinnerContainer).toBeInTheDocument();
  });

  it('should render loading text', () => {
    render(<Spinner />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should accept alignClass prop', () => {
    const { container } = render(<Spinner alignClass="text-center" />);
    
    // The component renders a div with a span containing "Loading..."
    expect(container.firstChild).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should render with default alignClass when not provided', () => {
    const { container } = render(<Spinner />);
    
    expect(container.firstChild).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should handle undefined alignClass', () => {
    render(<Spinner alignClass={undefined} />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should handle empty string alignClass', () => {
    render(<Spinner alignClass="" />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should render consistent structure', () => {
    const { container } = render(<Spinner />);
    
    // Check that it renders a div with a span
    const div = container.firstChild as HTMLElement;
    expect(div.tagName).toBe('DIV');
    
    const span = div.querySelector('span');
    expect(span).toBeInTheDocument();
    expect(span?.textContent).toBe('Loading...');
  });

  it('should be accessible', () => {
    render(<Spinner />);
    
    // The loading text should be accessible to screen readers
    const loadingText = screen.getByText('Loading...');
    expect(loadingText).toBeInTheDocument();
  });

  it('should handle multiple instances', () => {
    render(
      <div>
        <Spinner alignClass="first-spinner" />
        <Spinner alignClass="second-spinner" />
      </div>
    );
    
    const loadingTexts = screen.getAllByText('Loading...');
    expect(loadingTexts).toHaveLength(2);
  });
});
