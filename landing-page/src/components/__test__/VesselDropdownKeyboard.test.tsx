import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { VesselDropdown } from '../dashboard/VesselWidget/VesselDropdown';
import { VesselGroup } from '../../types/types';

// Mock the hooks and components
jest.mock('../../hooks/useDropdown', () => ({
  useDropdown: jest.fn(),
}));

jest.mock('lucide-react', () => ({
  ChevronDown: ({ size, className }: any) => (
    <div data-testid="chevron-down" className={className}>
      ChevronDown
    </div>
  ),
}));

jest.mock('../dashboard/VesselWidget/VesselDropdownMenu', () => ({
  VesselDropdownMenu: ({ searchTerm, onSearchChange, filteredGroups, selectedVessels }: any) => (
    <div data-testid="vessel-dropdown-menu" role="listbox">
      <input 
        data-testid="search-input"
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
      />
      <div>Groups: {filteredGroups.length}</div>
      <div>Selected: {selectedVessels.join(', ')}</div>
    </div>
  ),
}));

describe('VesselDropdown Keyboard Navigation', () => {
  const mockVesselGroups: VesselGroup[] = [
    {
      id: 1,
      title: 'Group 1',
      vessels: [
        { vessel_id: 1, name: 'Vessel Alpha', vessel_ownership_id: 101 },
        { vessel_id: 2, name: 'Vessel Beta', vessel_ownership_id: 102 },
      ],
    },
    {
      id: 2,
      title: 'Group 2',
      vessels: [
        { vessel_id: 3, name: 'Vessel Gamma', vessel_ownership_id: 103 },
        { vessel_id: 4, name: 'Vessel Delta', vessel_ownership_id: 104 },
      ],
    },
  ];

  const defaultProps = {
    groups: mockVesselGroups,
    selectedVessels: [],
    onSelectionChange: jest.fn(),
    placeholder: 'Select vessels',
    width: '300px',
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: false,
      toggleDropdown: jest.fn(),
    });
  });

  it('should open dropdown on Enter key press', () => {
    const toggleDropdown = jest.fn();
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: false,
      toggleDropdown,
    });

    render(<VesselDropdown {...defaultProps} />);
    
    const dropdownHeader = screen.getByRole('button');
    fireEvent.keyDown(dropdownHeader, { key: 'Enter' });
    
    expect(toggleDropdown).toHaveBeenCalled();
  });

  it('should open dropdown on Space key press', () => {
    const toggleDropdown = jest.fn();
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: false,
      toggleDropdown,
    });

    render(<VesselDropdown {...defaultProps} />);
    
    const dropdownHeader = screen.getByRole('button');
    fireEvent.keyDown(dropdownHeader, { key: ' ' });
    
    expect(toggleDropdown).toHaveBeenCalled();
  });

  it('should focus search input when dropdown opens', () => {
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(<VesselDropdown {...defaultProps} />);
    
    const searchInput = screen.getByTestId('search-input');
    expect(searchInput).toBeInTheDocument();
  });

  it('should close dropdown on Escape key press', () => {
    const toggleDropdown = jest.fn();
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown,
    });

    render(<VesselDropdown {...defaultProps} />);
    
    fireEvent.keyDown(screen.getByTestId('vessel-dropdown-menu'), { key: 'Escape' });
  });

  it('should update search term on input change', () => {
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(<VesselDropdown {...defaultProps} />);
    
    const searchInput = screen.getByTestId('search-input');
    fireEvent.change(searchInput, { target: { value: 'Alpha' } });
    
    expect(screen.getByDisplayValue('Alpha')).toBeInTheDocument();
  });

  it('should maintain focus when filtering with keyboard input', () => {
    const useDropdown = require('../../hooks/useDropdown').useDropdown;
    useDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(<VesselDropdown {...defaultProps} />);
    
    const searchInput = screen.getByTestId('search-input');
    searchInput.focus();
    fireEvent.change(searchInput, { target: { value: 'Alpha' } });
    
    expect(document.activeElement).toBe(searchInput);
  });
});
