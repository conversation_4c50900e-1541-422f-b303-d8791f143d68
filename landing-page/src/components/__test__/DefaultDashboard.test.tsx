import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DefaultDashboard } from '../dashboard/DefaultDashboard';
import { GlobalContext } from '../../context/dashboard-context';
const { updateUserAttribute } = require('../../services/user-service');

// Mocks
jest.mock('react-html-parser', () => () => <div data-testid="parsed-html">Parsed HTML</div>);
jest.mock('../../services/user-service', () => ({
    updateUserAttribute: jest.fn(() => Promise.resolve()),
}));
const mockGetTermCondition = jest.fn().mockResolvedValue({ data: { data: { CanvasContent1: '<p>Terms</p>', OData__UIVersionString: '1' } } });
jest.mock('../../services/sharepoint-service', () => ({
    getTermCondition: mockGetTermCondition
}));
jest.mock('../../utils/roles', () => ({
    hasAccessToVessel: jest.fn(() => true),
    hasKpiScorecardView: jest.fn(() => false),
    hasNovaView: jest.fn(() => true),
}));
jest.mock('../dashboard/VesselList', () => ({
    __esModule: true,
    default: () => <div>VesselList</div>
}));
jest.mock('../dashboard/TableauWidgets', () => ({
    __esModule: true,
    default: () => <div>TableauWidgets</div>
}));
const mockCrewInjuryStatistics = jest.fn().mockImplementation(() => <div>CrewInjuryStatistics</div>);
jest.mock('../dashboard/CrewInjuryStatistics', () => ({
    __esModule: true,
    default: jest.fn().mockImplementation((props, ref) => mockCrewInjuryStatistics(props))
}));
jest.mock('../dashboard/NoAccess', () => ({
    __esModule: true,
    default: () => <div>NoAccess</div>
}));
jest.mock('../dashboard/VesselWidget/VesselModuleContainer', () => ({
    __esModule: true,
    default: () => <div>VesselModuleContainer</div>
}));
jest.mock('../../pages/WorldMap', () => ({
    WorldMap: () => <div>WorldMap</div>,
}));
jest.mock('../common/index', () => ({
    CustomOverlay: (props: { run: boolean }) => (
        props.run ? <div data-testid="custom-overlay">CustomOverlay</div> : null
    ),
    GuardWrapper: ({ children }: { children: any }) => <>{children}</>
}));

const keycloakMock = {
    tokenParsed: {
        is_user_onboarded: false,
        ship_party_id: 123,
        ship_party_type: 'type',
        preferred_username: 'testuser',
        tc_version: undefined,
        email: '<EMAIL>',
        rank: 'user',
        name: 'Test User',
        user_id: 'test123',
        given_name: 'Test',
        family_name: 'User',
        group: ['testGroup'],
        ip_address: '127.0.0.1'
    },
    realmAccess: { roles: [] },
    idTokenParsed: {
        email: '<EMAIL>',
        rank: 'user',
        name: 'Test User',
        user_id: 'test123',
        given_name: 'Test',
        family_name: 'User',
        group: ['testGroup'],
        ip_address: '127.0.0.1',
        preferred_username: 'testuser'
    },
    authenticated: true,
    hasRealmRole: () => false,
    hasResourceRole: () => false,
    loadUserProfile: () => Promise.resolve({}),
    isTokenExpired: () => false,
    updateToken: () => Promise.resolve(true),
    login: () => {},
    logout: () => {},
    register: () => {},
    accountManagement: () => {},
    createLoginUrl: () => '',
    createLogoutUrl: () => '',
    createRegisterUrl: () => '',
    createAccountUrl: () => '',
    init: () => Promise.resolve(true),
};

const renderWithContext = (props = {}) =>
    render(
        <GlobalContext.Provider value={{
            ga4EventTrigger: jest.fn(),
            vesselCurrentlyOwned: [],
            isVesselLoading: false
        }}>
            <DefaultDashboard keycloak={{ ...keycloakMock, ...props }} />
        </GlobalContext.Provider>
    );

describe('DefaultDashboard', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers(); // Use fake timers to control setTimeout
    });

    afterEach(() => {
        jest.useRealTimers(); // Restore real timers after each test
    });

    it('renders dashboard components', async () => {
        renderWithContext();
        expect(await screen.findByText('TableauWidgets')).toBeInTheDocument();
        expect(screen.getByText('WorldMap')).toBeInTheDocument();
        expect(screen.getByText('VesselList')).toBeInTheDocument();
        expect(screen.getByText('CrewInjuryStatistics')).toBeInTheDocument();
        expect(screen.getByText('VesselModuleContainer')).toBeInTheDocument();
    });

    it('renders NOVA banner if user has Nova access', async () => {
        renderWithContext();
        expect(await screen.findByText(/Welcome to the new PARIS 2.0!/)).toBeInTheDocument();
    });


});