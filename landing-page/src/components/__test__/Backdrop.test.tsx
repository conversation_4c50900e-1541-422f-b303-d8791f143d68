import React from 'react';
import { render, screen } from '@testing-library/react';
import { Backdrop } from '../common/Backdrop';

describe('Backdrop Component', () => {
  it('renders the backdrop when show is true', () => {
    render(
      <Backdrop show={true}>
        <div data-testid="child">Child Content</div>
      </Backdrop>,
    );
    expect(screen.getByTestId('landing-page-backdrop')).toBeInTheDocument();
    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  it('does not render the backdrop when show is false', () => {
    render(
      <Backdrop show={false}>
        <div data-testid="child">Child Content</div>
      </Backdrop>,
    );
    expect(screen.queryByTestId('landing-page-backdrop')).not.toBeInTheDocument();
    expect(screen.queryByTestId('child')).not.toBeInTheDocument();
  });
});
