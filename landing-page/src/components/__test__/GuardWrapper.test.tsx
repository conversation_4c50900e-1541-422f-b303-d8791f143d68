import React from 'react';
import { render, screen } from '@testing-library/react';
import { GuardWrapper } from '../common/GuardWrapper';

jest.mock('../../StyleGuide', () => ({
  ErrorPage: jest.fn(({ errorCode, customMessage }) => (
    <div data-testid="error-page">
      Error {errorCode} - {customMessage}
    </div>
  )),
}));

describe('GuardWrapper Component', () => {
  it('renders children when hasAccess is true', () => {
    render(
      <GuardWrapper hasAccess={true}>
        <div data-testid="protected-content">Protected Content</div>
      </GuardWrapper>,
    );
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
  });

  it('renders ErrorPage when hasAccess is false and no fallback is provided', () => {
    render(<GuardWrapper hasAccess={false} customMessage="Access Denied" />);
    expect(screen.getByTestId('error-page')).toHaveTextContent('Error 403 - Access Denied');
  });

  it('renders the fallback component when hasAccess is false and fallback is provided', () => {
    render(
      <GuardWrapper
        hasAccess={false}
        fallback={<div data-testid="fallback">Fallback Content</div>}
      />,
    );
    expect(screen.getByTestId('fallback')).toBeInTheDocument();
    expect(screen.queryByTestId('error-page')).not.toBeInTheDocument();
  });

  it('renders nothing when hasAccess is false and fallback is explicitly null', () => {
    const { container } = render(<GuardWrapper hasAccess={false} fallback={null} />);
    expect(container.firstChild).toBeNull();
  });
});
