import React from 'react';
import { render, screen } from '@testing-library/react';
import VesselTrackPolyline from '../dashboard/WorldMap/Polyline';
import createGoogleMapsMock from './createGoogleMapsMock';
import { vesselTracksData } from '../../services/__mocks__/vessel-service';

jest.mock('../../StyleGuide.tsx');
jest.mock('../../services/user-service');
jest.mock('../../services/vessel-service');

beforeAll(() => {
  window.google = {
    maps: createGoogleMapsMock(),
  };
});

afterAll(() => {
  delete window.google;
});

describe('Should render the vessel track component', () => {
  const isVesselTrackLoading = false;
  it('Should render a vessel track', () => {
    render(
      <VesselTrackPolyline
        vesselTracksData={vesselTracksData}
        isVesselTrackLoading={isVesselTrackLoading}
      />,
    );
    const polylineElement = screen.getByTestId('track-polyline');
    expect(polylineElement).toBeInTheDocument();
  });

  it('Should render one dot for each data point along the vessel track', () => {
    render(
      <VesselTrackPolyline
        vesselTracksData={vesselTracksData}
        isVesselTrackLoading={isVesselTrackLoading}
      />,
    );
    const markerElements = screen.getAllByTestId('track-marker');
    expect(markerElements.length).toBe(vesselTracksData.length);
  });
});
