export interface VesselSubscription {
  created_at: string | Date;
  end_date: string | Date | null;
  id: number;
  subscription_plan_name?: string | null;
  ref_id: number | null;
  last_updated_by: string;
  ship_party_id: number;
  start_date: string | Date;
  subscription_plan_id: number;
  updated_at: string | Date;
  vessel_id: number;
  vessel_name?: string;
  ship_party_type: string | undefined;
}